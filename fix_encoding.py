#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix encoding issues in the project
Replace Unicode symbols with ASCII equivalents for Windows compatibility
"""

import os
import re

def fix_file_encoding(filepath):
    """Fix encoding issues in a single file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace Unicode symbols with ASCII equivalents
        replacements = {
            '✅': '[OK]',
            '❌': '[ERROR]',
            '✗': '[FAIL]',
            '✓': '[OK]',
            '⚠️': '[WARNING]',
            '🎉': '[SUCCESS]',
            '🔍': '[SEARCH]',
            '🌐': '[WEB]',
            '🧪': '[TEST]',
            '📊': '[STATS]',
            '💾': '[SAVE]',
            '📁': '[FILES]',
            '🚀': '[START]',
            '⏹️': '[STOP]',
            '🛡️': '[SECURITY]',
            '⚙️': '[CONFIG]',
            '📞': '[SUPPORT]',
            '⚡': '[PERFORMANCE]',
            '🎲': '[RANDOM]',
            '🤖': '[BOT]',
            '🖥️': '[GUI]',
            '🇻🇳': '[VN]',
            '📝': '[LOG]',
            '🔄': '[ROTATE]',
            '💰': '[PAID]',
            '🔧': '[TOOLS]',
            '📋': '[LIST]',
            '🛠️': '[REPAIR]',
            '📚': '[DOCS]',
            '💡': '[TIP]',
        }
        
        original_content = content
        for unicode_char, ascii_replacement in replacements.items():
            content = content.replace(unicode_char, ascii_replacement)
        
        # Only write if content changed
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"[OK] Fixed encoding in {filepath}")
            return True
        else:
            print(f"[SKIP] No changes needed in {filepath}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Failed to fix {filepath}: {str(e)}")
        return False

def fix_logging_setup():
    """Fix logging setup to use UTF-8 encoding"""
    files_to_fix = [
        'main.py',
        'update_proxies.py',
        'test_tool.py',
        'setup.py'
    ]
    
    for filepath in files_to_fix:
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Fix logging.FileHandler to include encoding
                patterns = [
                    (r"logging\.FileHandler\('([^']+)'\)", r"logging.FileHandler('\1', encoding='utf-8')"),
                    (r'logging\.FileHandler\("([^"]+)"\)', r'logging.FileHandler("\1", encoding="utf-8")'),
                ]
                
                original_content = content
                for pattern, replacement in patterns:
                    content = re.sub(pattern, replacement, content)
                
                if content != original_content:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"[OK] Fixed logging setup in {filepath}")
                
            except Exception as e:
                print(f"[ERROR] Failed to fix logging in {filepath}: {str(e)}")

def main():
    """Main function to fix all encoding issues"""
    print("=" * 60)
    print("Fixing Encoding Issues")
    print("=" * 60)
    
    # Files to fix
    files_to_check = [
        'main.py',
        'modules/proxy_manager.py',
        'modules/data_generator.py',
        'modules/registration_bot.py',
        'modules/proxy_fetcher.py',
        'update_proxies.py',
        'simple_proxy_update.py',
        'test_tool.py',
        'test_proxy_update.py',
        'demo.py',
        'setup.py',
        'build_exe.py',
        'project_summary.py',
        'README.md',
        'USAGE_GUIDE.md',
        'PROXY_GUIDE.md'
    ]
    
    fixed_count = 0
    
    # Fix Unicode symbols
    print("\n[STEP 1] Fixing Unicode symbols...")
    for filepath in files_to_check:
        if os.path.exists(filepath):
            if fix_file_encoding(filepath):
                fixed_count += 1
        else:
            print(f"[SKIP] File not found: {filepath}")
    
    # Fix logging setup
    print("\n[STEP 2] Fixing logging setup...")
    fix_logging_setup()
    
    # Create a Windows-compatible run script
    print("\n[STEP 3] Creating Windows-compatible scripts...")
    
    # Update run.bat to set UTF-8 encoding
    run_bat_content = '''@echo off
chcp 65001 >nul
echo ================================
echo Auto Registration Tool
echo ================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking dependencies...

REM Install dependencies if needed
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

echo Activating virtual environment...
call venv\\Scripts\\activate.bat

echo Installing/updating dependencies...
pip install -r requirements.txt

echo.
echo Starting Auto Registration Tool...
echo.

python main.py

echo.
echo Tool closed. Press any key to exit...
pause >nul
'''
    
    try:
        with open('run.bat', 'w', encoding='utf-8') as f:
            f.write(run_bat_content)
        print("[OK] Updated run.bat with UTF-8 support")
    except Exception as e:
        print(f"[ERROR] Failed to update run.bat: {str(e)}")
    
    # Create a simple test script
    test_encoding_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test encoding compatibility
"""

def test_encoding():
    """Test if encoding issues are fixed"""
    print("Testing encoding compatibility...")
    
    # Test ASCII symbols
    symbols = {
        'OK': '[OK]',
        'ERROR': '[ERROR]',
        'FAIL': '[FAIL]',
        'WARNING': '[WARNING]',
        'SUCCESS': '[SUCCESS]'
    }
    
    for name, symbol in symbols.items():
        print(f"{name}: {symbol}")
    
    print("\\n[OK] Encoding test completed successfully!")

if __name__ == "__main__":
    test_encoding()
'''
    
    try:
        with open('test_encoding.py', 'w', encoding='utf-8') as f:
            f.write(test_encoding_content)
        print("[OK] Created test_encoding.py")
    except Exception as e:
        print(f"[ERROR] Failed to create test_encoding.py: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"[COMPLETE] Fixed encoding in {fixed_count} files")
    print("\nRecommendations:")
    print("1. Use run.bat to start the tool on Windows")
    print("2. Run 'python test_encoding.py' to verify fixes")
    print("3. Check logs for any remaining encoding issues")
    print("=" * 60)

if __name__ == "__main__":
    main()
