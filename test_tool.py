#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Auto Registration Tool
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_generator import DataGenerator
from modules.proxy_manager import ProxyManager
import json

def test_data_generator():
    """Test data generation"""
    print("=== Testing Data Generator ===")
    
    generator = DataGenerator()
    
    # Generate sample data
    for i in range(3):
        data = generator.generate_user_data()
        print(f"\nSample {i+1}:")
        print(f"Username: {data['username']}")
        print(f"Email: {data['email']}")
        print(f"Password: {data['password']}")
        print(f"Full Name: {data['full_name']}")
        print(f"Phone: {data['phone']}")
        print(f"Birth Date: {data['birth_date']}")
        print(f"Address: {data['address']}")
    
    print("\n[OK] Data Generator test completed")

def test_proxy_manager():
    """Test proxy management"""
    print("\n=== Testing Proxy Manager ===")
    
    proxy_manager = ProxyManager()
    
    # Test proxy parsing
    test_proxies = [
        "123.456.789.0:8080",
        "user:pass@123.456.789.0:8080",
        "invalid_proxy",
        "***********:3128"
    ]
    
    print("Testing proxy parsing:")
    for proxy in test_proxies:
        result = proxy_manager.parse_proxy(proxy)
        print(f"  {proxy} -> {result}")
    
    # Test proxy formatting for selenium
    valid_proxy = "123.456.789.0:8080"
    selenium_format = proxy_manager.format_proxy_for_selenium(valid_proxy)
    print(f"\nSelenium format for {valid_proxy}:")
    print(f"  {selenium_format}")
    
    print("\n[OK] Proxy Manager test completed")

def test_config_loading():
    """Test configuration loading"""
    print("\n=== Testing Configuration ===")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("Configuration loaded successfully:")
        print(f"  Default URL: {config['default_settings']['target_url']}")
        print(f"  Account Count: {config['default_settings']['account_count']}")
        print(f"  Delay: {config['default_settings']['delay_seconds']}s")
        print(f"  Headless: {config['default_settings']['headless_mode']}")
        print(f"  Use Proxy: {config['default_settings']['use_proxy']}")
        
        print(f"\nField mappings loaded: {len(config['field_mappings'])} types")
        print(f"Selectors loaded: {len(config['selectors'])} categories")
        
        print("\n[OK] Configuration test completed")
        
    except Exception as e:
        print(f"[ERROR] Configuration test failed: {str(e)}")

def test_requirements():
    """Test if all required packages are installed"""
    print("\n=== Testing Requirements ===")
    
    required_packages = [
        'selenium',
        'requests',
        'fake_useragent',
        'faker',
        'beautifulsoup4',
        'lxml',
        'webdriver_manager'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"[OK] {package}")
        except ImportError:
            print(f"[ERROR] {package} - NOT INSTALLED")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n[ERROR] Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
    else:
        print("\n[OK] All requirements satisfied")

def main():
    """Run all tests"""
    print("Auto Registration Tool - Test Suite")
    print("=" * 50)
    
    try:
        test_requirements()
        test_config_loading()
        test_data_generator()
        test_proxy_manager()
        
        print("\n" + "=" * 50)
        print("[SUCCESS] All tests completed successfully!")
        print("\nYou can now run the main tool with: python main.py")
        
    except Exception as e:
        print(f"\n[ERROR] Test failed with error: {str(e)}")
        print("Please check the error and try again.")

if __name__ == "__main__":
    main()
