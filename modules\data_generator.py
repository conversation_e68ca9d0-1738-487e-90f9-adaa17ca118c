#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Generator Module
Generates random user data for registration
"""

import random
import string
import time
from faker import Faker
import logging

class DataGenerator:
    def __init__(self, locale='en_US'):
        self.faker = Faker(locale)
        self.logger = logging.getLogger(__name__)
        
        # Common email domains
        self.email_domains = [
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'protonmail.com', 'tempmail.org', '10minutemail.com',
            'guerrillamail.com', 'mailinator.com'
        ]
        
        # Vietnamese names for more realistic data
        self.vietnamese_first_names = [
            '<PERSON>h', '<PERSON>', '<PERSON><PERSON>ơng', '<PERSON><PERSON>', '<PERSON>ấn', '<PERSON><PERSON><PERSON>', '<PERSON>n', '<PERSON>',
            '<PERSON><PERSON>ơng', '<PERSON>uang', 'Thả<PERSON>', 'Việt', '<PERSON><PERSON>', '<PERSON>ến', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
            '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'
        ]
        
        self.vietnamese_last_names = [
            '<PERSON><PERSON>ễ<PERSON>', '<PERSON>r<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ng', '<PERSON><PERSON>nh', 'Phan', 'Vũ',
            'V<PERSON>', 'Đặng', 'B<PERSON>i', 'Đỗ', 'Hồ', 'Ngô', 'Dương', 'Lý'
        ]
    
    def generate_username(self, length=8):
        """Generate random username"""
        # Mix of letters and numbers
        chars = string.ascii_lowercase + string.digits
        username = ''.join(random.choices(chars, k=length))
        
        # Sometimes add a prefix/suffix
        if random.choice([True, False]):
            prefixes = ['user', 'player', 'gamer', 'pro', 'vip']
            username = random.choice(prefixes) + username
        
        return username
    
    def generate_email(self, username=None):
        """Generate random email address"""
        if not username:
            username = self.generate_username()
        
        domain = random.choice(self.email_domains)
        
        # Sometimes add numbers or dots
        if random.choice([True, False]):
            username += str(random.randint(1, 999))
        
        if random.choice([True, False]) and '.' not in username:
            # Insert dot in middle
            mid = len(username) // 2
            username = username[:mid] + '.' + username[mid:]
        
        return f"{username}@{domain}"
    
    def generate_password(self, length=12, include_special=True):
        """Generate random password"""
        chars = string.ascii_letters + string.digits
        if include_special:
            chars += "!@#$%^&*"
        
        # Ensure password has at least one of each type
        password = [
            random.choice(string.ascii_lowercase),
            random.choice(string.ascii_uppercase),
            random.choice(string.digits)
        ]
        
        if include_special:
            password.append(random.choice("!@#$%^&*"))
        
        # Fill the rest randomly
        for _ in range(length - len(password)):
            password.append(random.choice(chars))
        
        # Shuffle the password
        random.shuffle(password)
        return ''.join(password)
    
    def generate_phone_number(self, country_code='+84'):
        """Generate Vietnamese phone number"""
        # Vietnamese mobile prefixes
        prefixes = ['90', '91', '92', '93', '94', '95', '96', '97', '98', '99',
                   '86', '87', '88', '89', '85', '84', '83', '82', '81', '70']
        
        prefix = random.choice(prefixes)
        number = ''.join([str(random.randint(0, 9)) for _ in range(7)])
        
        return f"{country_code}{prefix}{number}"
    
    def generate_vietnamese_name(self):
        """Generate Vietnamese name"""
        first_name = random.choice(self.vietnamese_first_names)
        last_name = random.choice(self.vietnamese_last_names)
        
        # Sometimes add middle name
        if random.choice([True, False]):
            middle_name = random.choice(self.vietnamese_first_names)
            return f"{last_name} {middle_name} {first_name}"
        else:
            return f"{last_name} {first_name}"
    
    def generate_birth_date(self, min_age=18, max_age=65):
        """Generate random birth date"""
        return self.faker.date_of_birth(minimum_age=min_age, maximum_age=max_age)
    
    def generate_address(self):
        """Generate Vietnamese address"""
        cities = [
            'Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ',
            'Biên Hòa', 'Huế', 'Nha Trang', 'Buôn Ma Thuột', 'Quy Nhon'
        ]
        
        districts = [
            'Quận 1', 'Quận 2', 'Quận 3', 'Quận 4', 'Quận 5',
            'Quận Bình Thạnh', 'Quận Tân Bình', 'Quận Phú Nhuận',
            'Quận Gò Vấp', 'Quận Thủ Đức'
        ]
        
        streets = [
            'Nguyễn Huệ', 'Lê Lợi', 'Trần Hưng Đạo', 'Hai Bà Trưng',
            'Nguyễn Thái Học', 'Điện Biên Phủ', 'Cách Mạng Tháng 8',
            'Võ Văn Tần', 'Nam Kỳ Khởi Nghĩa', 'Lý Tự Trọng'
        ]
        
        street_number = random.randint(1, 999)
        street = random.choice(streets)
        district = random.choice(districts)
        city = random.choice(cities)
        
        return f"{street_number} {street}, {district}, {city}"
    
    def generate_user_data(self):
        """Generate complete user data for registration"""
        username = self.generate_username()
        
        data = {
            'username': username,
            'email': self.generate_email(username),
            'password': self.generate_password(),
            'full_name': self.generate_vietnamese_name(),
            'phone': self.generate_phone_number(),
            'birth_date': self.generate_birth_date(),
            'address': self.generate_address(),
            'gender': random.choice(['male', 'female']),
            'timestamp': int(time.time())
        }
        
        # Add some additional fields that might be needed
        data.update({
            'first_name': data['full_name'].split()[-1],
            'last_name': ' '.join(data['full_name'].split()[:-1]),
            'confirm_password': data['password'],
            'agree_terms': True,
            'newsletter': random.choice([True, False])
        })
        
        return data
    
    def generate_user_agent(self):
        """Generate random user agent"""
        browsers = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36'
        ]
        return random.choice(browsers)
    
    def generate_referral_code(self, length=8):
        """Generate referral code"""
        chars = string.ascii_uppercase + string.digits
        return ''.join(random.choices(chars, k=length))
    
    def save_generated_data(self, data, filename):
        """Save generated data to file"""
        try:
            import json
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            return True
        except Exception as e:
            self.logger.error(f"Failed to save data: {str(e)}")
            return False
