# Hướng dẫn Proxy cho Auto Registration Tool

## [LIST] Tổng quan

Proxy là máy chủ trung gian giúp ẩn địa chỉ IP thật của bạn khi đăng ký tài kho<PERSON>n, tr<PERSON>h bị phát hiện và chặn.

## [VN] Proxy Việt Nam

### Tại sao sử dụng proxy Việt Nam?
- [OK] Tốc đ<PERSON> hơn (gần về địa lý)
- [OK] Ít bị chặn bởi website Việt Nam
- [OK] Phù hợp với timezone và ngôn ngữ
- [OK] Tránh bị phát hiện là traffic nước ngoài

### Nhà cung cấp Internet Việt Nam:
- **FPT Telecom**: 171.244.x.x, 27.64.x.x, 27.65.x.x
- **Viettel**: 113.160.x.x, 14.160.x.x, 14.161.x.x
- **VNPT**: 115.73.x.x, 116.96.x.x
- **CMC Telecom**: 103.9.x.x, 103.152.x.x

## [TOOLS] C<PERSON>ch sử dụng Proxy trong Tool

### 1. Thêm proxy thủ công:
```
Tab "Proxy Management" → Paste danh sách proxy → "Test Proxies"
```

### 2. Tự động cập nhật:
```
Tab "Proxy Management" → "Auto Update VN" → Chờ kết quả
```

### 3. Load từ file:
```
Tab "Proxy Management" → "Load from File" → Chọn file .txt
```

## [LOG] Định dạng Proxy

### Proxy thường (không authentication):
```
ip:port
***************:8080
**************:53281
**************:9999
```

### Proxy có authentication:
```
username:password@ip:port
myuser:mypass@***************:8080
user123:pass456@**************:53281
```

## [WEB] Nguồn Proxy Miễn phí

### Websites cung cấp proxy miễn phí:
1. **Free Proxy List**: https://free-proxy-list.net/
2. **Proxy List Download**: https://www.proxy-list.download/
3. **GeoNode**: https://proxylist.geonode.com/
4. **Spys.one**: http://spys.one/en/free-proxy-list/
5. **ProxyScrape**: https://api.proxyscrape.com/

### Lọc proxy Việt Nam:
- Tìm country code: **VN** hoặc **Vietnam**
- Kiểm tra IP ranges của các ISP Việt Nam
- Sử dụng tool auto-update trong ứng dụng

## [PAID] Proxy Trả phí (Khuyến nghị)

### Ưu điểm proxy trả phí:
- [OK] Tốc độ cao, ổn định
- [OK] Uptime 99%+
- [OK] Hỗ trợ kỹ thuật
- [OK] Ít bị chặn
- [OK] Rotation tự động

### Nhà cung cấp uy tín:
1. **Bright Data** (brightdata.com)
   - Proxy chất lượng cao
   - Có proxy Việt Nam
   - Giá: $500+/tháng

2. **Smartproxy** (smartproxy.com)
   - Residential proxy
   - Có location Việt Nam
   - Giá: $75+/tháng

3. **ProxyMesh** (proxymesh.com)
   - Datacenter proxy
   - Giá rẻ hơn
   - Giá: $10+/tháng

4. **Storm Proxies** (stormproxies.com)
   - Rotating proxy
   - Giá cạnh tranh
   - Giá: $50+/tháng

## [TEST] Test Proxy

### Trong tool:
```
1. Thêm proxy vào list
2. Click "Test Proxies"
3. Chờ kết quả ([OK] working / [ERROR] failed)
4. Chỉ sử dụng proxy working
```

### Test thủ công:
```bash
# Sử dụng curl
curl --proxy http://***************:8080 http://httpbin.org/ip

# Sử dụng Python
import requests
proxies = {'http': 'http://***************:8080'}
response = requests.get('http://httpbin.org/ip', proxies=proxies)
print(response.text)
```

## [CONFIG] Cấu hình Proxy

### Trong tool:
- **Use proxy**: [OK] Bật sử dụng proxy
- **Proxy rotation**: Tự động xoay vòng proxy
- **Timeout**: 10-30 giây
- **Max retries**: 3 lần

### Best practices:
- Sử dụng 10-50 proxy cho 100 tài khoản
- Delay 5-10 giây giữa các request
- Test proxy trước khi chạy batch lớn
- Backup nhiều nguồn proxy

## 🚨 Lưu ý Bảo mật

### [WARNING] Rủi ro proxy miễn phí:
- Có thể log traffic của bạn
- Tốc độ chậm, không ổn định
- Có thể chứa malware
- Thông tin có thể bị đánh cắp

### [SECURITY] Bảo vệ bản thân:
- [ERROR] Không nhập thông tin nhạy cảm qua proxy miễn phí
- [OK] Sử dụng HTTPS khi có thể
- [OK] Kiểm tra proxy từ nguồn tin cậy
- [OK] Thay đổi proxy thường xuyên
- [OK] Sử dụng VPN kết hợp nếu cần

## [ROTATE] Auto Update Proxy

### Tính năng trong tool:
```
Tab "Proxy Management" → "Auto Update VN"
```

### Quy trình:
1. Fetch proxy từ nhiều nguồn
2. Lọc chỉ proxy Việt Nam
3. Test connectivity
4. Cập nhật danh sách working proxy
5. Lưu vào file

### Tùy chỉnh:
- Chỉnh sửa `modules/proxy_fetcher.py`
- Thêm nguồn proxy mới
- Thay đổi logic lọc
- Điều chỉnh timeout

## [STATS] Monitoring Proxy

### Thống kê trong tool:
- Số proxy đã load
- Số proxy working
- Tỷ lệ thành công
- Proxy hiện tại đang dùng

### Log proxy:
```
[12:34:56] Loaded proxy file: proxies.txt
[12:34:57] Testing 50 proxies...
[12:35:10] [OK] ***************:8080 - Working
[12:35:11] [FAIL] **************:53281 - Failed
[12:35:15] Proxy test completed: 15/50 working
```

## [REPAIR] Troubleshooting

### Proxy không hoạt động:
1. Kiểm tra định dạng: `ip:port`
2. Test proxy thủ công
3. Thử proxy khác
4. Kiểm tra firewall/antivirus
5. Thử port khác (80, 8080, 3128)

### Tốc độ chậm:
1. Sử dụng proxy gần hơn
2. Thử datacenter proxy thay vì residential
3. Tăng timeout
4. Giảm số concurrent requests

### Bị chặn:
1. Thay đổi User-Agent
2. Sử dụng proxy khác
3. Tăng delay giữa requests
4. Sử dụng residential proxy

## [DOCS] Tài liệu tham khảo

### APIs:
- ProxyScrape API: https://docs.proxyscrape.com/
- Free Proxy API: https://www.proxy-list.download/api/
- IP Geolocation: https://ipapi.co/

### Tools:
- Proxy Checker: https://hidemy.name/en/proxy-checker/
- IP Lookup: https://whatismyipaddress.com/
- Speed Test: https://www.speedtest.net/

### Học thêm:
- Proxy vs VPN: https://www.cloudflare.com/learning/access-management/proxy-vs-vpn/
- HTTP Proxy: https://developer.mozilla.org/en-US/docs/Web/HTTP/Proxy_servers_and_tunneling
- SOCKS Proxy: https://en.wikipedia.org/wiki/SOCKS
