# Hướng dẫn sử dụng chi tiết - Auto Registration Tool

## [START] Bắt đầu nhanh

### 1. Cài đặt và chạy
```bash
# Cách 1: Sử dụng setup script
python setup.py

# Cách 2: <PERSON><PERSON><PERSON> đặt thủ công
pip install -r requirements.txt
python main.py

# Cách 3: Sử dụng batch file (Windows)
run.bat
```

### 2. Cấu hình cơ bản
1. Mở tool → Tab "Configuration"
2. Nhập URL trang web: `https://www.13win16.com/?id=391111507`
3. Đặt số lượng tài khoản: `10`
4. Đặt delay: `5` giây
5. <PERSON><PERSON><PERSON> chế độ headless nếu muốn chạy nhanh

## [LIST] Hướng dẫn từng bước

### Bước 1: Chuẩn bị Proxy (Tù<PERSON> chọn)

#### Lấy proxy miễn phí:
- https://free-proxy-list.net/
- https://www.proxy-list.download/
- https://www.freeproxylists.net/

#### Định dạng proxy:
```
# Proxy thường
123.456.789.0:8080
***********:3128

# Proxy có authentication
username:password@123.456.789.0:8080
myuser:<EMAIL>:3128
```

#### Thêm proxy vào tool:
1. Tab "Proxy Management"
2. Dán danh sách proxy (mỗi dòng một proxy)
3. Hoặc "Load from File" → chọn file .txt
4. Click "Test Proxies" để kiểm tra

### Bước 2: Cấu hình đăng ký

#### Tab Configuration:
- **Target URL**: URL trang đăng ký
- **Number of accounts**: Số tài khoản cần tạo (1-1000)
- **Delay**: Thời gian chờ giữa các lần đăng ký (1-60 giây)
- **Headless mode**: [OK] Chạy ẩn (nhanh) / [ERROR] Hiện browser (debug)
- **Use proxy**: [OK] Sử dụng proxy / [ERROR] Không dùng proxy

### Bước 3: Chạy đăng ký

1. Tab "Control"
2. Click "Start Registration"
3. Theo dõi tiến trình:
   - Progress bar: Tiến độ hoàn thành
   - Statistics: Thống kê thành công/thất bại
   - Logs: Chi tiết từng bước

### Bước 4: Xem kết quả

#### Tab Control:
- **Progress**: Hiển thị tiến độ hiện tại
- **Statistics**: Số liệu thành công/thất bại
- **Save Results**: Lưu kết quả ra file JSON

#### Tab Logs:
- **Real-time logs**: Theo dõi quá trình thời gian thực
- **Save Logs**: Lưu log ra file TXT
- **Clear Logs**: Xóa log hiện tại

## [CONFIG] Tùy chỉnh nâng cao

### Chỉnh sửa field mapping

File: `modules/registration_bot.py` → `fill_registration_fields()`

```python
field_mappings = {
    'username': ['username', 'user', 'login', 'account', 'taikhoan'],
    'email': ['email', 'mail', 'e-mail', 'email_address'],
    'password': ['password', 'pass', 'pwd', 'matkhau'],
    # Thêm mapping cho trang web cụ thể
    'referral_code': ['ref', 'referral', 'invite_code']
}
```

### Chỉnh sửa selectors

File: `config.json` → `selectors`

```json
{
  "registration_buttons": [
    "//a[contains(text(), 'Đăng ký')]",
    "//button[contains(@class, 'register-btn')]",
    "//input[@value='Register']"
  ]
}
```

### Thêm xử lý captcha

File: `modules/registration_bot.py` → `solve_simple_captcha()`

```python
def solve_simple_captcha(self, driver):
    # Thêm logic xử lý captcha cụ thể
    # Ví dụ: OCR, API giải captcha, etc.
    pass
```

## [TOOLS] Xử lý sự cố

### Lỗi thường gặp:

#### 1. ChromeDriver không tìm thấy
```bash
# Giải pháp:
pip install --upgrade webdriver-manager
```

#### 2. Proxy không hoạt động
- Kiểm tra định dạng proxy
- Test proxy trước khi sử dụng
- Thử proxy từ nguồn khác

#### 3. Form không được điền
- Chạy ở chế độ không headless để debug
- Kiểm tra selector trong code
- Xem screenshot được tạo tự động

#### 4. Trang web chặn bot
- Tăng delay giữa các request
- Sử dụng proxy xoay vòng
- Thay đổi user agent
- Thêm random behavior

### Debug mode:

1. Tắt headless mode
2. Giảm số lượng tài khoản xuống 1-2
3. Tăng delay lên 10-15 giây
4. Xem browser hoạt động trực tiếp

## [STATS] Tối ưu hiệu suất

### Cài đặt tối ưu:

#### Đăng ký nhanh:
- Headless: [OK]
- Proxy: [ERROR] (nếu không bị chặn)
- Delay: 2-3 giây
- Số lượng: 50-100

#### Đăng ký an toàn:
- Headless: [OK]
- Proxy: [OK] (xoay vòng)
- Delay: 5-10 giây
- Số lượng: 10-20

#### Debug mode:
- Headless: [ERROR]
- Proxy: [ERROR]
- Delay: 10-15 giây
- Số lượng: 1-2

## [SECURITY] Bảo mật và đạo đức

### Sử dụng có trách nhiệm:
- [OK] Chỉ đăng ký cho mục đích hợp pháp
- [OK] Tuân thủ Terms of Service
- [OK] Không spam hoặc lạm dụng
- [OK] Sử dụng delay hợp lý

### Bảo vệ thông tin:
- [ERROR] Không chia sẻ proxy có authentication
- [ERROR] Không lưu mật khẩu thật trong code
- [OK] Sử dụng proxy từ nguồn tin cậy
- [OK] Xóa log nhạy cảm sau khi dùng

## 📈 Mở rộng tính năng

### Thêm tính năng mới:

1. **Email verification**: Tự động xác thực email
2. **Captcha solving**: Tích hợp API giải captcha
3. **Multi-threading**: Đăng ký song song
4. **Database storage**: Lưu kết quả vào DB
5. **Telegram notification**: Thông báo qua bot

### Tích hợp API:

```python
# Ví dụ: Tích hợp 2captcha
def solve_captcha_with_api(self, captcha_image):
    # Gửi captcha đến API
    # Nhận kết quả và điền vào form
    pass
```

## [SUPPORT] Hỗ trợ

### Khi gặp vấn đề:

1. **Kiểm tra log**: Tab Logs → xem lỗi chi tiết
2. **Chạy test**: `python test_tool.py`
3. **Debug mode**: Tắt headless, giảm số lượng
4. **Screenshot**: Xem ảnh chụp màn hình tự động

### Báo lỗi:

Khi báo lỗi, vui lòng cung cấp:
- URL trang web
- Log lỗi chi tiết
- Screenshot (nếu có)
- Cấu hình đã sử dụng

## 🎯 Tips & Tricks

### Tăng tỷ lệ thành công:
1. **Proxy chất lượng**: Sử dụng proxy tốc độ cao
2. **Delay phù hợp**: Không quá nhanh, không quá chậm
3. **User agent đa dạng**: Thay đổi browser signature
4. **Dữ liệu thực tế**: Sử dụng thông tin hợp lý

### Tránh bị phát hiện:
1. **Random behavior**: Thêm độ trễ ngẫu nhiên
2. **Mouse movement**: Mô phỏng cử động chuột
3. **Typing speed**: Gõ với tốc độ người thật
4. **Session management**: Quản lý cookie và session

### Monitoring:
1. **Success rate**: Theo dõi tỷ lệ thành công
2. **Error patterns**: Phân tích lỗi thường gặp
3. **Performance**: Đo thời gian xử lý
4. **Resource usage**: Kiểm tra CPU/RAM
