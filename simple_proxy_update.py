#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple proxy updater without external dependencies
"""

import requests
import time
import random

def get_vietnam_proxy_ranges():
    """Get Vietnam IP ranges"""
    return [
        '103.9.',      # FPT
        '103.152.',    # Various ISPs
        '113.160.',    # Viettel
        '115.73.',     # VNPT
        '171.244.',    # FPT
        '14.160.',     # Viettel
        '14.161.',     # Viettel
        '27.64.',      # FPT
        '27.65.',      # FPT
        '116.96.',     # VNPT
        '116.97.',     # VNPT
        '125.212.',    # Various
        '125.213.',    # Various
    ]

def is_vietnam_ip(ip):
    """Check if IP is from Vietnam"""
    vietnam_ranges = get_vietnam_proxy_ranges()
    return any(ip.startswith(prefix) for prefix in vietnam_ranges)

def generate_vietnam_proxies():
    """Generate potential Vietnam proxy IPs"""
    proxies = []
    vietnam_ranges = get_vietnam_proxy_ranges()
    common_ports = [80, 8080, 3128, 8888, 9999, 53281, 1080]

    for ip_prefix in vietnam_ranges[:5]:  # Use first 5 ranges
        for i in range(1, 255, 20):  # Sample every 20th IP
            for port in random.sample(common_ports, 2):  # Random 2 ports
                ip = f"{ip_prefix}{i}"
                proxy = f"{ip}:{port}"
                proxies.append(proxy)

    return proxies[:30]  # Limit to 30 proxies

def fetch_proxies_from_api():
    """Fetch proxies from API sources"""
    proxies = []

    # Try different API endpoints
    api_urls = [
        "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=5000&country=VN&format=textplain",
        "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
        "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt"
    ]

    for url in api_urls:
        try:
            print(f"Trying API: {url[:50]}...")
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                proxy_list = response.text.strip().split('\n')
                for proxy in proxy_list:
                    proxy = proxy.strip()
                    if proxy and ':' in proxy:
                        ip = proxy.split(':')[0]
                        if is_vietnam_ip(ip):
                            proxies.append(proxy)
                            print(f"Found VN proxy: {proxy}")

            time.sleep(1)  # Be nice to servers

        except Exception as e:
            print(f"Error with {url}: {str(e)}")

    return proxies

def test_proxy(proxy, timeout=5):
    """Test if a proxy is working"""
    try:
        proxy_dict = {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }

        response = requests.get(
            'http://httpbin.org/ip',
            proxies=proxy_dict,
            timeout=timeout,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )

        if response.status_code == 200:
            return True
    except:
        pass

    return False

def update_proxy_file(proxies, filename='example_proxies.txt'):
    """Update proxy file with new proxies"""
    try:
        content = f"""# Vietnam Free Proxy List - Auto Updated
# Last updated: {time.strftime('%Y-%m-%d %H:%M:%S')}
# Total proxies: {len(proxies)}
# Format: ip:port

"""
        for proxy in proxies:
            content += f"{proxy}\n"

        content += """
# Note: These are free proxies and may be unstable
# Test before using with "Test Proxies" button
# For better reliability, consider paid proxy services
"""

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"[OK] Updated {filename} with {len(proxies)} proxies")
        return True
    except Exception as e:
        print(f"[ERROR] Error updating file: {str(e)}")
        return False

def main():
    """Main function"""
    print("=" * 50)
    print("Simple Vietnam Proxy Updater")
    print("=" * 50)

    all_proxies = []

    # Method 1: Generate potential proxies
    print("[RANDOM] Generating potential Vietnam proxies...")
    generated = generate_vietnam_proxies()
    all_proxies.extend(generated)
    print(f"Generated {len(generated)} potential proxies")

    # Method 2: Fetch from APIs
    print("\n[WEB] Fetching from API sources...")
    api_proxies = fetch_proxies_from_api()
    all_proxies.extend(api_proxies)
    print(f"Found {len(api_proxies)} proxies from APIs")

    # Remove duplicates
    unique_proxies = list(set(all_proxies))
    print(f"\n[STATS] Total unique proxies: {len(unique_proxies)}")

    if not unique_proxies:
        print("[ERROR] No proxies found!")
        return False

    # Test a sample of proxies
    print("\n[TEST] Testing proxy connectivity...")
    working_proxies = []
    test_sample = unique_proxies[:20]  # Test first 20

    for i, proxy in enumerate(test_sample, 1):
        print(f"Testing {i}/{len(test_sample)}: {proxy}", end=" ... ")
        if test_proxy(proxy):
            working_proxies.append(proxy)
            print("[OK] Working")
        else:
            print("[FAIL] Failed")

    if working_proxies:
        print(f"\n[SUCCESS] Found {len(working_proxies)} working proxies!")

        # Update proxy file
        update_proxy_file(working_proxies)

        print("\nFiles updated:")
        print("  - example_proxies.txt")

        return True
    else:
        print("\n[ERROR] No working proxies found!")
        print("Try running again later or check your internet connection")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n[ERROR] Update failed")
    except KeyboardInterrupt:
        print("\n[STOP] Update cancelled by user")
    except Exception as e:
        print(f"\n[ERROR] Unexpected error: {str(e)}")
