#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test for proxy update functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_proxy_fetcher():
    """Test proxy fetcher basic functionality"""
    try:
        from modules.proxy_fetcher import ProxyFetcher
        
        print("🧪 Testing ProxyFetcher...")
        fetcher = ProxyFetcher()
        
        # Test Vietnam IP detection
        test_ips = [
            "************",      # Should be Vietnam
            "***************",   # Should be Vietnam
            "*******",          # Should not be Vietnam
            "**************"     # Should be Vietnam
        ]
        
        print("Testing Vietnam IP detection:")
        for ip in test_ips:
            is_vn = fetcher.is_vietnam_ip(ip)
            print(f"  {ip}: {'✅ Vietnam' if is_vn else '❌ Not Vietnam'}")
        
        # Test proxy generation
        print("\nGenerating sample Vietnam proxies:")
        generated = fetcher.generate_vietnam_proxies()
        for i, proxy in enumerate(generated[:5]):
            print(f"  {i+1}. {proxy}")
        
        print(f"\n✅ Generated {len(generated)} sample proxies")
        
        # Test saving
        test_proxies = ["************:3128", "***************:80", "**************:53281"]
        success = fetcher.save_proxies_to_file(test_proxies, "test_proxies.txt")
        if success:
            print("✅ Proxy saving test passed")
        else:
            print("❌ Proxy saving test failed")
        
        return True
        
    except Exception as e:
        print(f"❌ ProxyFetcher test failed: {str(e)}")
        return False

def test_proxy_manager():
    """Test proxy manager with sample data"""
    try:
        from modules.proxy_manager import ProxyManager
        
        print("\n🧪 Testing ProxyManager...")
        manager = ProxyManager()
        
        # Test proxy parsing
        test_proxies = [
            "************:3128",
            "user:pass@***************:80",
            "invalid_proxy",
            "**************:53281"
        ]
        
        print("Testing proxy parsing:")
        for proxy in test_proxies:
            parsed = manager.parse_proxy(proxy)
            if parsed:
                print(f"  ✅ {proxy} -> {parsed['ip']}:{parsed['port']}")
            else:
                print(f"  ❌ {proxy} -> Invalid")
        
        # Test proxy formatting
        valid_proxy = "************:3128"
        selenium_format = manager.format_proxy_for_selenium(valid_proxy)
        print(f"\nSelenium format test:")
        print(f"  {valid_proxy} -> {selenium_format}")
        
        return True
        
    except Exception as e:
        print(f"❌ ProxyManager test failed: {str(e)}")
        return False

def create_sample_proxy_file():
    """Create a sample proxy file with Vietnam proxies"""
    vietnam_proxies = [
        "# Vietnam Free Proxies - Sample",
        "# Updated: 2024",
        "",
        "# HTTP Proxies",
        "************:3128",
        "************:3128",
        "***************:80",
        "***************:80",
        "",
        "# HTTPS Proxies", 
        "**************:53281",
        "**************:53281",
        "**************:9999",
        "**************:9999",
        "",
        "# FPT Proxies",
        "171.244.140.160:8080",
        "171.244.140.161:8080",
        "",
        "# Note: These are sample proxies for testing"
    ]
    
    try:
        with open('vietnam_proxies_sample.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(vietnam_proxies))
        print("✅ Created sample proxy file: vietnam_proxies_sample.txt")
        return True
    except Exception as e:
        print(f"❌ Failed to create sample file: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Proxy Update System - Test Suite")
    print("=" * 60)
    
    try:
        # Test 1: ProxyFetcher
        if not test_proxy_fetcher():
            return False
        
        # Test 2: ProxyManager
        if not test_proxy_manager():
            return False
        
        # Test 3: Create sample file
        if not create_sample_proxy_file():
            return False
        
        print("\n" + "=" * 60)
        print("🎉 All proxy tests passed!")
        print("\nNext steps:")
        print("1. Run: python update_proxies.py --quick")
        print("2. Or use the GUI 'Auto Update VN' button")
        print("3. Check example_proxies.txt for updated list")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n❌ Tests failed. Please check the errors above.")
        sys.exit(1)
