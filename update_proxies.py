#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto Proxy Updater
Automatically fetch and test fresh Vietnam proxies
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.proxy_fetcher import ProxyFetcher
from modules.proxy_manager import ProxyManager
import logging
import time

def setup_logging():
    """Setup logging for proxy updater"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('proxy_update.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def main():
    """Main proxy update function"""
    print("=" * 60)
    print("Vietnam Proxy Auto Updater")
    print("=" * 60)
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize fetcher and manager
        fetcher = ProxyFetcher()
        manager = ProxyManager()
        
        # Fetch fresh proxies
        print("[SEARCH] Fetching fresh Vietnam proxies...")
        proxies = fetcher.fetch_all_vietnam_proxies()
        
        if not proxies:
            print("[ERROR] No proxies found!")
            return False
        
        print(f"[OK] Found {len(proxies)} potential proxies")
        
        # Test proxies
        print("[TEST] Testing proxy connectivity...")
        working_proxies = manager.test_proxies(proxies, max_workers=20, timeout=5)
        
        if not working_proxies:
            print("[ERROR] No working proxies found!")
            print("[TIP] Try again later or use different sources")
            return False
        
        print(f"[OK] Found {len(working_proxies)} working proxies")
        
        # Save results
        print("[SAVE] Saving results...")
        
        # Save all proxies
        fetcher.save_proxies_to_file(proxies, "all_vietnam_proxies.txt")
        
        # Save working proxies
        fetcher.save_proxies_to_file(working_proxies, "working_vietnam_proxies.txt")
        
        # Update example file
        fetcher.update_example_proxies(working_proxies)
        
        # Save working proxies for the tool
        manager.working_proxies = working_proxies
        manager.save_working_proxies("verified_proxies.txt")
        
        print("\n" + "=" * 60)
        print("[SUCCESS] Proxy update completed successfully!")
        print(f"[STATS] Statistics:")
        print(f"   Total found: {len(proxies)}")
        print(f"   Working: {len(working_proxies)}")
        print(f"   Success rate: {len(working_proxies)/len(proxies)*100:.1f}%")
        print(f"\n[FILES] Files updated:")
        print(f"   - example_proxies.txt (for GUI)")
        print(f"   - all_vietnam_proxies.txt (all found)")
        print(f"   - working_vietnam_proxies.txt (tested)")
        print(f"   - verified_proxies.txt (for tool)")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"Proxy update failed: {str(e)}")
        print(f"[ERROR] Error: {str(e)}")
        return False

def quick_update():
    """Quick update with minimal output"""
    try:
        fetcher = ProxyFetcher()
        manager = ProxyManager()
        
        # Fetch and test
        proxies = fetcher.fetch_all_vietnam_proxies()
        if proxies:
            working_proxies = manager.test_proxies(proxies[:20], max_workers=10, timeout=3)
            if working_proxies:
                fetcher.update_example_proxies(working_proxies)
                print(f"[OK] Updated with {len(working_proxies)} working proxies")
                return True
        
        print("[ERROR] No working proxies found")
        return False
        
    except Exception as e:
        print(f"[ERROR] Quick update failed: {str(e)}")
        return False

def interactive_update():
    """Interactive proxy update"""
    while True:
        print("\n" + "=" * 50)
        print("Vietnam Proxy Updater - Interactive Mode")
        print("=" * 50)
        print("1. Full update (fetch + test all)")
        print("2. Quick update (fetch + test 20)")
        print("3. Fetch only (no testing)")
        print("4. Test existing proxies")
        print("5. View current proxy stats")
        print("6. Exit")
        print()
        
        try:
            choice = input("Select option (1-6): ").strip()
            
            if choice == "1":
                main()
            elif choice == "2":
                quick_update()
            elif choice == "3":
                fetcher = ProxyFetcher()
                proxies = fetcher.fetch_all_vietnam_proxies()
                fetcher.save_proxies_to_file(proxies, "fetched_proxies.txt")
                print(f"[OK] Fetched {len(proxies)} proxies (not tested)")
            elif choice == "4":
                try:
                    with open('example_proxies.txt', 'r') as f:
                        lines = f.readlines()
                    proxies = [line.strip() for line in lines if line.strip() and not line.startswith('#')]
                    
                    if proxies:
                        manager = ProxyManager()
                        working = manager.test_proxies(proxies)
                        print(f"[OK] {len(working)}/{len(proxies)} proxies are working")
                    else:
                        print("[ERROR] No proxies found in example_proxies.txt")
                except Exception as e:
                    print(f"[ERROR] Error testing proxies: {str(e)}")
            elif choice == "5":
                try:
                    files = ['example_proxies.txt', 'all_vietnam_proxies.txt', 'working_vietnam_proxies.txt']
                    for file in files:
                        if os.path.exists(file):
                            with open(file, 'r') as f:
                                lines = f.readlines()
                            proxy_count = len([line for line in lines if line.strip() and not line.startswith('#')])
                            print(f"[FILES] {file}: {proxy_count} proxies")
                        else:
                            print(f"[FILES] {file}: Not found")
                except Exception as e:
                    print(f"[ERROR] Error reading stats: {str(e)}")
            elif choice == "6":
                print("Goodbye!")
                break
            else:
                print("Invalid option. Please try again.")
                
        except KeyboardInterrupt:
            print("\nUpdate interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
        
        if choice != "6":
            input("\nPress Enter to continue...")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--quick":
            quick_update()
        elif sys.argv[1] == "--interactive":
            interactive_update()
        elif sys.argv[1] == "--help":
            print("Vietnam Proxy Updater")
            print("Usage:")
            print("  python update_proxies.py           # Full update")
            print("  python update_proxies.py --quick   # Quick update")
            print("  python update_proxies.py --interactive  # Interactive mode")
            print("  python update_proxies.py --help    # Show this help")
        else:
            print("Unknown option. Use --help for usage.")
    else:
        main()
