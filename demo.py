#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo script - Quick test without GUI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_generator import DataGenerator
from modules.proxy_manager import ProxyManager
from modules.registration_bot import RegistrationBot
import time

def demo_data_generation():
    """Demo data generation"""
    print("🎲 Generating sample user data...")
    
    generator = DataGenerator()
    data = generator.generate_user_data()
    
    print(f"Username: {data['username']}")
    print(f"Email: {data['email']}")
    print(f"Password: {data['password']}")
    print(f"Full Name: {data['full_name']}")
    print(f"Phone: {data['phone']}")
    print(f"Address: {data['address']}")
    print()

def demo_proxy_test():
    """Demo proxy testing"""
    print("🌐 Testing sample proxies...")
    
    # Sample free proxies (may not work)
    sample_proxies = [
        "***************:80",
        "**************:53281",
        "************:53281"
    ]
    
    proxy_manager = ProxyManager()
    working_proxies = proxy_manager.test_proxies(sample_proxies, timeout=5)
    
    print(f"Working proxies: {len(working_proxies)}/{len(sample_proxies)}")
    for proxy in working_proxies:
        print(f"  ✅ {proxy}")
    print()

def demo_registration_test():
    """Demo registration test (dry run)"""
    print("🤖 Testing registration bot (dry run)...")
    
    # Test URL (replace with actual target)
    test_url = "https://www.13win16.com/?id=391111507"
    
    generator = DataGenerator()
    
    # Create bot instance
    bot = RegistrationBot(
        url=test_url,
        proxy_list=[],  # No proxy for demo
        headless=False,  # Show browser for demo
        delay=3,
        data_generator=generator
    )
    
    print(f"Target URL: {test_url}")
    print("Bot configured successfully!")
    print("Note: This is a dry run - no actual registration will be performed")
    print()

def interactive_demo():
    """Interactive demo menu"""
    while True:
        print("=" * 50)
        print("Auto Registration Tool - Demo")
        print("=" * 50)
        print("1. Generate sample user data")
        print("2. Test proxy connectivity")
        print("3. Test registration bot setup")
        print("4. Run full GUI application")
        print("5. Exit")
        print()
        
        try:
            choice = input("Select option (1-5): ").strip()
            
            if choice == "1":
                demo_data_generation()
            elif choice == "2":
                demo_proxy_test()
            elif choice == "3":
                demo_registration_test()
            elif choice == "4":
                print("Starting GUI application...")
                os.system("python main.py")
                break
            elif choice == "5":
                print("Goodbye!")
                break
            else:
                print("Invalid option. Please try again.")
                
        except KeyboardInterrupt:
            print("\nDemo interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
        
        input("\nPress Enter to continue...")

def quick_demo():
    """Quick demo of all features"""
    print("🚀 Auto Registration Tool - Quick Demo")
    print("=" * 50)
    
    # Demo 1: Data generation
    demo_data_generation()
    
    # Demo 2: Proxy testing
    demo_proxy_test()
    
    # Demo 3: Bot setup
    demo_registration_test()
    
    print("✅ Demo completed!")
    print("\nTo run the full application:")
    print("  python main.py")
    print("\nTo run interactive demo:")
    print("  python demo.py --interactive")

def main():
    """Main demo function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_demo()
    else:
        quick_demo()

if __name__ == "__main__":
    main()
