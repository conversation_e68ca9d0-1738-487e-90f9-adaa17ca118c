#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup script for Auto Registration Tool
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("[ERROR] Python 3.7+ is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"[OK] Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def install_requirements():
    """Install required packages"""
    print("Installing requirements...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("[OK] Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] Failed to install requirements: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ["logs", "screenshots", "results"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"[OK] Created directory: {directory}")

def check_chrome():
    """Check if Chrome is installed"""
    system = platform.system().lower()
    
    chrome_paths = {
        'windows': [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ],
        'linux': [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser"
        ],
        'darwin': [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        ]
    }
    
    if system in chrome_paths:
        for path in chrome_paths[system]:
            if os.path.exists(path):
                print("[OK] Chrome browser found")
                return True
    
    print("[WARNING]  Chrome browser not found")
    print("Please install Google Chrome from: https://www.google.com/chrome/")
    return False

def run_test():
    """Run test script"""
    print("\nRunning tests...")
    try:
        subprocess.check_call([sys.executable, "test_tool.py"])
        return True
    except subprocess.CalledProcessError:
        print("[ERROR] Tests failed")
        return False

def main():
    """Main setup function"""
    print("=" * 60)
    print("Auto Registration Tool - Setup")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check Chrome
    check_chrome()
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Run tests
    if not run_test():
        print("\n[WARNING]  Setup completed with warnings")
        print("Some tests failed, but you can still try running the tool")
    else:
        print("\n[SUCCESS] Setup completed successfully!")
    
    print("\n" + "=" * 60)
    print("Next steps:")
    print("1. Edit example_proxies.txt with your proxy list")
    print("2. Run the tool: python main.py")
    print("3. Or use the batch file: run.bat (Windows) / run.sh (Linux/Mac)")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n[ERROR] Setup failed. Please check the errors above.")
        sys.exit(1)
    
    # Ask if user wants to run the tool now
    try:
        response = input("\nDo you want to run the tool now? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            subprocess.call([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\nSetup completed. You can run the tool later with: python main.py")
    except Exception as e:
        print(f"Error: {e}")
        print("You can run the tool manually with: python main.py")
