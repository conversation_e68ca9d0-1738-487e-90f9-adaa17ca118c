#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Project Summary Generator
Creates a comprehensive overview of the Auto Registration Tool
"""

import os
import time

def count_lines_in_file(filepath):
    """Count lines in a file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return len(f.readlines())
    except:
        return 0

def get_file_size(filepath):
    """Get file size in KB"""
    try:
        return os.path.getsize(filepath) / 1024
    except:
        return 0

def analyze_project():
    """Analyze project structure and generate summary"""
    
    # Project files
    files = {
        'Core Files': [
            'main.py',
            'modules/proxy_manager.py',
            'modules/data_generator.py', 
            'modules/registration_bot.py',
            'modules/proxy_fetcher.py'
        ],
        'Configuration': [
            'requirements.txt',
            'config.json',
            'example_proxies.txt'
        ],
        'Documentation': [
            'README.md',
            'USAGE_GUIDE.md',
            'PROXY_GUIDE.md'
        ],
        'Scripts': [
            'setup.py',
            'test_tool.py',
            'demo.py',
            'update_proxies.py',
            'simple_proxy_update.py',
            'test_proxy_update.py',
            'build_exe.py'
        ],
        'Run Scripts': [
            'run.bat',
            'run.sh'
        ]
    }
    
    print("=" * 80)
    print("AUTO REGISTRATION TOOL - PROJECT SUMMARY")
    print("=" * 80)
    print(f"Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Project overview
    print("[LIST] PROJECT OVERVIEW")
    print("-" * 40)
    print("Tool Name: Auto Registration Tool")
    print("Version: 1.0")
    print("Language: Python 3.7+")
    print("GUI Framework: Tkinter")
    print("Automation: Selenium WebDriver")
    print("Target: https://www.13win16.com/?id=391111507")
    print()
    
    # Features
    print("✨ KEY FEATURES")
    print("-" * 40)
    features = [
        "[GUI]  Modern GUI with tabbed interface",
        "[WEB] Proxy support (HTTP/HTTPS/SOCKS)",
        "[VN] Vietnam proxy auto-update",
        "[RANDOM] Random Vietnamese user data generation",
        "[BOT] Selenium-based form automation",
        "[STATS] Real-time progress tracking",
        "[LOG] Detailed logging system",
        "[TEST] Proxy testing functionality",
        "[CONFIG]  Configurable settings",
        "[SAVE] Export results to JSON/TXT",
        "[ROTATE] Multi-threading support",
        "[SECURITY]  Error handling & recovery"
    ]
    
    for feature in features:
        print(f"  {feature}")
    print()
    
    # File analysis
    total_lines = 0
    total_size = 0
    
    print("[FILES] PROJECT STRUCTURE")
    print("-" * 40)
    
    for category, file_list in files.items():
        print(f"\n{category}:")
        category_lines = 0
        category_size = 0
        
        for filepath in file_list:
            if os.path.exists(filepath):
                lines = count_lines_in_file(filepath)
                size = get_file_size(filepath)
                category_lines += lines
                category_size += size
                total_lines += lines
                total_size += size
                
                print(f"  [OK] {filepath:<30} {lines:>4} lines  {size:>6.1f} KB")
            else:
                print(f"  [ERROR] {filepath:<30} Missing")
        
        print(f"     Subtotal: {category_lines:>4} lines  {category_size:>6.1f} KB")
    
    print(f"\n[STATS] TOTAL: {total_lines:>4} lines  {total_size:>6.1f} KB")
    print()
    
    # Dependencies
    print("📦 DEPENDENCIES")
    print("-" * 40)
    try:
        with open('requirements.txt', 'r') as f:
            deps = f.readlines()
        
        for dep in deps:
            dep = dep.strip()
            if dep and not dep.startswith('#'):
                print(f"  📌 {dep}")
    except:
        print("  [ERROR] requirements.txt not found")
    print()
    
    # Usage instructions
    print("[START] QUICK START")
    print("-" * 40)
    print("1. Install dependencies:")
    print("   pip install -r requirements.txt")
    print()
    print("2. Run setup (optional):")
    print("   python setup.py")
    print()
    print("3. Start the tool:")
    print("   python main.py")
    print("   # or")
    print("   run.bat  (Windows)")
    print("   run.sh   (Linux/Mac)")
    print()
    print("4. Configure and run:")
    print("   - Tab 'Configuration': Set URL, count, delay")
    print("   - Tab 'Proxy Management': Add proxies")
    print("   - Tab 'Control': Start registration")
    print()
    
    # Advanced usage
    print("[CONFIG]  ADVANCED USAGE")
    print("-" * 40)
    print("• Test components:")
    print("  python test_tool.py")
    print()
    print("• Update Vietnam proxies:")
    print("  python update_proxies.py")
    print("  python simple_proxy_update.py")
    print()
    print("• Interactive demo:")
    print("  python demo.py --interactive")
    print()
    print("• Build executable:")
    print("  python build_exe.py")
    print()
    
    # Configuration
    print("[TOOLS] CONFIGURATION")
    print("-" * 40)
    print("• Main config: config.json")
    print("• Proxy list: example_proxies.txt")
    print("• Logs: registration.log")
    print("• Results: Auto-generated JSON files")
    print()
    
    # Security notes
    print("[SECURITY]  SECURITY & ETHICS")
    print("-" * 40)
    print("[WARNING]  IMPORTANT WARNINGS:")
    print("• Only use for legitimate purposes")
    print("• Respect website Terms of Service")
    print("• Don't spam or abuse services")
    print("• Use reasonable delays between requests")
    print("• Test proxies from trusted sources")
    print("• Don't share authenticated proxy credentials")
    print()
    
    # Support
    print("[SUPPORT] SUPPORT & TROUBLESHOOTING")
    print("-" * 40)
    print("• Check logs in 'Logs' tab")
    print("• Run test_tool.py for diagnostics")
    print("• Use non-headless mode for debugging")
    print("• Verify Chrome browser installation")
    print("• Test proxy connectivity first")
    print("• Read USAGE_GUIDE.md for details")
    print("• Check PROXY_GUIDE.md for proxy help")
    print()
    
    # Performance tips
    print("[PERFORMANCE] PERFORMANCE TIPS")
    print("-" * 40)
    print("• Use headless mode for speed")
    print("• Optimize delay settings (5-10 seconds)")
    print("• Use working proxies only")
    print("• Limit concurrent registrations")
    print("• Monitor system resources")
    print("• Use SSD for better I/O performance")
    print()
    
    print("=" * 80)
    print("[SUCCESS] PROJECT ANALYSIS COMPLETE")
    print("=" * 80)

def save_summary():
    """Save summary to file"""
    import sys
    from io import StringIO
    
    # Capture output
    old_stdout = sys.stdout
    sys.stdout = captured_output = StringIO()
    
    analyze_project()
    
    # Restore stdout
    sys.stdout = old_stdout
    
    # Save to file
    summary_content = captured_output.getvalue()
    
    try:
        with open('PROJECT_SUMMARY.txt', 'w', encoding='utf-8') as f:
            f.write(summary_content)
        print("[OK] Project summary saved to PROJECT_SUMMARY.txt")
    except Exception as e:
        print(f"[ERROR] Failed to save summary: {str(e)}")
    
    # Also print to console
    print(summary_content)

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--save":
        save_summary()
    else:
        analyze_project()

if __name__ == "__main__":
    main()
