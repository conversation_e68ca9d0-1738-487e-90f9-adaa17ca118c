#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build executable script using PyInstaller
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """Install PyInstaller if not available"""
    try:
        import PyInstaller
        print("[OK] PyInstaller already installed")
        return True
    except ImportError:
        print("Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("[OK] PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("[ERROR] Failed to install PyInstaller")
            return False

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('modules', 'modules'),
        ('config.json', '.'),
        ('example_proxies.txt', '.'),
        ('README.md', '.'),
        ('USAGE_GUIDE.md', '.')
    ],
    hiddenimports=[
        'selenium',
        'webdriver_manager',
        'fake_useragent',
        'faker',
        'requests',
        'beautifulsoup4',
        'lxml'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AutoRegistrationTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None
)
'''
    
    with open('AutoRegistrationTool.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("[OK] Spec file created")

def build_executable():
    """Build the executable"""
    print("Building executable...")
    
    try:
        # Build using spec file
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "AutoRegistrationTool.spec"
        ])
        
        print("[OK] Executable built successfully!")
        
        # Check if exe exists
        exe_path = os.path.join("dist", "AutoRegistrationTool.exe")
        if os.path.exists(exe_path):
            print(f"[FILES] Executable location: {exe_path}")
            print(f"📏 File size: {os.path.getsize(exe_path) / (1024*1024):.1f} MB")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] Build failed: {e}")
        return False

def create_portable_package():
    """Create portable package with all files"""
    print("Creating portable package...")
    
    package_dir = "AutoRegistrationTool_Portable"
    
    # Create package directory
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    os.makedirs(package_dir)
    
    # Copy executable
    exe_src = os.path.join("dist", "AutoRegistrationTool.exe")
    if os.path.exists(exe_src):
        shutil.copy2(exe_src, package_dir)
    
    # Copy important files
    files_to_copy = [
        "README.md",
        "USAGE_GUIDE.md",
        "example_proxies.txt",
        "config.json"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, package_dir)
    
    # Create directories
    os.makedirs(os.path.join(package_dir, "logs"), exist_ok=True)
    os.makedirs(os.path.join(package_dir, "results"), exist_ok=True)
    os.makedirs(os.path.join(package_dir, "screenshots"), exist_ok=True)
    
    # Create run script
    run_script = '''@echo off
echo Starting Auto Registration Tool...
echo.
AutoRegistrationTool.exe
echo.
echo Tool closed. Press any key to exit...
pause >nul
'''
    
    with open(os.path.join(package_dir, "run.bat"), 'w') as f:
        f.write(run_script)
    
    print(f"[OK] Portable package created: {package_dir}")
    print("[FILES] Package contents:")
    for root, dirs, files in os.walk(package_dir):
        level = root.replace(package_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")

def cleanup():
    """Clean up build files"""
    print("Cleaning up...")
    
    cleanup_items = [
        "build",
        "__pycache__",
        "AutoRegistrationTool.spec"
    ]
    
    for item in cleanup_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
            print(f"🗑️  Removed {item}")

def main():
    """Main build function"""
    print("=" * 60)
    print("Auto Registration Tool - Build Executable")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        print("[ERROR] main.py not found. Please run this script from the project root.")
        return False
    
    # Install PyInstaller
    if not install_pyinstaller():
        return False
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if not build_executable():
        return False
    
    # Create portable package
    create_portable_package()
    
    # Ask about cleanup
    try:
        response = input("\nDo you want to clean up build files? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            cleanup()
    except KeyboardInterrupt:
        print("\nBuild completed.")
    
    print("\n" + "=" * 60)
    print("[SUCCESS] Build completed successfully!")
    print("\nYou can now distribute:")
    print("1. Single executable: dist/AutoRegistrationTool.exe")
    print("2. Portable package: AutoRegistrationTool_Portable/")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n[ERROR] Build failed. Please check the errors above.")
        sys.exit(1)
