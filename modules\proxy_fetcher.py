#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Proxy <PERSON><PERSON>er <PERSON>
Automatically fetch fresh Vietnam proxies from free sources
"""

import requests
import re
import time
import json
import logging
import random
try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False
    print("Warning: BeautifulSoup4 not available. Some features will be limited.")

try:
    from concurrent.futures import ThreadPoolExecutor, as_completed
    HAS_CONCURRENT = True
except ImportError:
    HAS_CONCURRENT = False
    print("Warning: concurrent.futures not available. Will use sequential processing.")

class ProxyFetcher:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

        # Vietnam IP ranges for filtering
        self.vietnam_ip_ranges = [
            '103.9.',      # FPT
            '103.152.',    # Various ISPs
            '113.160.',    # Viettel
            '115.73.',     # VNPT
            '171.244.',    # FPT
            '14.160.',     # Viettel
            '14.161.',     # Viettel
            '14.162.',     # Viettel
            '14.163.',     # Viettel
            '14.164.',     # Viettel
            '14.165.',     # Viettel
            '14.166.',     # Viettel
            '14.167.',     # Viettel
            '14.168.',     # Viettel
            '14.169.',     # Viettel
            '14.170.',     # Viettel
            '14.171.',     # Viettel
            '14.172.',     # Viettel
            '14.173.',     # Viettel
            '14.174.',     # Viettel
            '14.175.',     # Viettel
            '14.176.',     # Viettel
            '14.177.',     # Viettel
            '14.178.',     # Viettel
            '14.179.',     # Viettel
            '14.180.',     # Viettel
            '14.181.',     # Viettel
            '14.182.',     # Viettel
            '14.183.',     # Viettel
            '14.184.',     # Viettel
            '14.185.',     # Viettel
            '14.186.',     # Viettel
            '14.187.',     # Viettel
            '14.188.',     # Viettel
            '14.189.',     # Viettel
            '14.190.',     # Viettel
            '14.191.',     # Viettel
            '27.64.',      # FPT
            '27.65.',      # FPT
            '27.66.',      # FPT
            '27.67.',      # FPT
            '27.68.',      # FPT
            '27.69.',      # FPT
            '27.70.',      # FPT
            '27.71.',      # FPT
            '27.72.',      # FPT
            '27.73.',      # FPT
            '27.74.',      # FPT
            '27.75.',      # FPT
            '27.76.',      # FPT
            '27.77.',      # FPT
            '27.78.',      # FPT
            '27.79.',      # FPT
            '116.96.',     # VNPT
            '116.97.',     # VNPT
            '116.98.',     # VNPT
            '116.99.',     # VNPT
            '116.100.',    # VNPT
            '116.101.',    # VNPT
            '116.102.',    # VNPT
            '116.103.',    # VNPT
            '116.104.',    # VNPT
            '116.105.',    # VNPT
            '116.106.',    # VNPT
            '116.107.',    # VNPT
            '116.108.',    # VNPT
            '116.109.',    # VNPT
            '116.110.',    # VNPT
            '116.111.',    # VNPT
            '125.212.',    # Various
            '125.213.',    # Various
            '125.214.',    # Various
            '125.215.',    # Various
            '125.216.',    # Various
            '125.217.',    # Various
            '125.218.',    # Various
            '125.219.',    # Various
            '125.220.',    # Various
            '125.221.',    # Various
            '125.222.',    # Various
            '125.223.',    # Various
            '125.224.',    # Various
            '125.225.',    # Various
            '125.226.',    # Various
            '125.227.',    # Various
            '125.228.',    # Various
            '125.229.',    # Various
            '125.230.',    # Various
            '125.231.',    # Various
            '125.232.',    # Various
            '125.233.',    # Various
            '125.234.',    # Various
            '125.235.',    # Various
            '125.236.',    # Various
            '125.237.',    # Various
            '125.238.',    # Various
            '125.239.',    # Various
        ]

    def is_vietnam_ip(self, ip):
        """Check if IP is from Vietnam"""
        return any(ip.startswith(prefix) for prefix in self.vietnam_ip_ranges)

    def fetch_from_free_proxy_list(self):
        """Fetch proxies from free-proxy-list.net"""
        proxies = []
        try:
            url = "https://free-proxy-list.net/"
            response = self.session.get(url, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')

            table = soup.find('table', {'id': 'proxylisttable'})
            if table:
                rows = table.find('tbody').find_all('tr')
                for row in rows:
                    cols = row.find_all('td')
                    if len(cols) >= 7:
                        ip = cols[0].text.strip()
                        port = cols[1].text.strip()
                        country = cols[2].text.strip()

                        # Filter for Vietnam or Vietnam IPs
                        if country == 'VN' or self.is_vietnam_ip(ip):
                            proxy = f"{ip}:{port}"
                            proxies.append(proxy)
                            self.logger.info(f"Found VN proxy: {proxy}")

        except Exception as e:
            self.logger.error(f"Error fetching from free-proxy-list: {str(e)}")

        return proxies

    def fetch_from_proxy_list_download(self):
        """Fetch proxies from proxy-list.download"""
        proxies = []
        try:
            # Try different endpoints
            endpoints = [
                "https://www.proxy-list.download/api/v1/get?type=http&country=VN",
                "https://www.proxy-list.download/api/v1/get?type=https&country=VN",
                "https://www.proxy-list.download/api/v1/get?type=socks4&country=VN",
                "https://www.proxy-list.download/api/v1/get?type=socks5&country=VN"
            ]

            for endpoint in endpoints:
                try:
                    response = self.session.get(endpoint, timeout=10)
                    if response.status_code == 200:
                        proxy_list = response.text.strip().split('\n')
                        for proxy in proxy_list:
                            proxy = proxy.strip()
                            if proxy and ':' in proxy:
                                ip = proxy.split(':')[0]
                                if self.is_vietnam_ip(ip):
                                    proxies.append(proxy)
                                    self.logger.info(f"Found VN proxy: {proxy}")
                except Exception as e:
                    self.logger.warning(f"Error with endpoint {endpoint}: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error fetching from proxy-list.download: {str(e)}")

        return proxies

    def fetch_from_proxylist_geonode(self):
        """Fetch proxies from geonode.com"""
        proxies = []
        try:
            url = "https://proxylist.geonode.com/api/proxy-list?limit=500&page=1&sort_by=lastChecked&sort_type=desc&country=VN"
            response = self.session.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    for proxy_info in data['data']:
                        ip = proxy_info.get('ip')
                        port = proxy_info.get('port')
                        country = proxy_info.get('country')

                        if country == 'VN' and ip and port:
                            proxy = f"{ip}:{port}"
                            proxies.append(proxy)
                            self.logger.info(f"Found VN proxy: {proxy}")

        except Exception as e:
            self.logger.error(f"Error fetching from geonode: {str(e)}")

        return proxies

    def fetch_from_spys_one(self):
        """Fetch proxies from spys.one"""
        proxies = []
        try:
            url = "http://spys.one/en/free-proxy-list/"
            response = self.session.get(url, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Find proxy table
            table = soup.find('table', {'class': 'spy1x'})
            if table:
                rows = table.find_all('tr')
                for row in rows[2:]:  # Skip header rows
                    cols = row.find_all('td')
                    if len(cols) >= 6:
                        proxy_cell = cols[0]
                        country_cell = cols[3]

                        if proxy_cell and country_cell:
                            proxy_text = proxy_cell.get_text(strip=True)
                            country_text = country_cell.get_text(strip=True)

                            # Look for Vietnam
                            if 'VN' in country_text or 'Vietnam' in country_text:
                                # Extract IP:PORT from the cell
                                proxy_match = re.search(r'(\d+\.\d+\.\d+\.\d+):(\d+)', proxy_text)
                                if proxy_match:
                                    proxy = f"{proxy_match.group(1)}:{proxy_match.group(2)}"
                                    proxies.append(proxy)
                                    self.logger.info(f"Found VN proxy: {proxy}")

        except Exception as e:
            self.logger.error(f"Error fetching from spys.one: {str(e)}")

        return proxies

    def generate_vietnam_proxies(self):
        """Generate potential Vietnam proxy IPs"""
        proxies = []

        # Common ports for proxies
        common_ports = [80, 8080, 3128, 8888, 9999, 53281, 1080, 8000, 8001, 8002]

        # Generate some potential IPs based on known Vietnam ranges
        for ip_prefix in self.vietnam_ip_ranges[:10]:  # Limit to first 10 ranges
            for i in range(1, 255, 10):  # Sample every 10th IP
                for port in random.sample(common_ports, 3):  # Random 3 ports
                    ip = f"{ip_prefix}{i}"
                    proxy = f"{ip}:{port}"
                    proxies.append(proxy)

        return proxies[:50]  # Limit to 50 generated proxies

    def fetch_all_vietnam_proxies(self):
        """Fetch Vietnam proxies from all sources"""
        self.logger.info("Starting to fetch Vietnam proxies from all sources...")

        all_proxies = []

        # Fetch from different sources
        sources = [
            ("Free Proxy List", self.fetch_from_free_proxy_list),
            ("Proxy List Download", self.fetch_from_proxy_list_download),
            ("Geonode", self.fetch_from_proxylist_geonode),
            ("Spys One", self.fetch_from_spys_one),
            ("Generated", self.generate_vietnam_proxies)
        ]

        for source_name, fetch_func in sources:
            try:
                self.logger.info(f"Fetching from {source_name}...")
                proxies = fetch_func()
                all_proxies.extend(proxies)
                self.logger.info(f"Found {len(proxies)} proxies from {source_name}")
                time.sleep(1)  # Be nice to servers
            except Exception as e:
                self.logger.error(f"Error fetching from {source_name}: {str(e)}")

        # Remove duplicates
        unique_proxies = list(set(all_proxies))
        self.logger.info(f"Total unique Vietnam proxies found: {len(unique_proxies)}")

        return unique_proxies

    def save_proxies_to_file(self, proxies, filename="vietnam_proxies.txt"):
        """Save proxies to file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"# Vietnam Free Proxies - Auto Updated\n")
                f.write(f"# Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Total proxies: {len(proxies)}\n\n")

                for proxy in proxies:
                    f.write(f"{proxy}\n")

            self.logger.info(f"Saved {len(proxies)} proxies to {filename}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving proxies: {str(e)}")
            return False

    def update_example_proxies(self, proxies):
        """Update the example_proxies.txt file"""
        try:
            content = f"""# Vietnam Free Proxy List - Auto Updated
# Last updated: {time.strftime('%Y-%m-%d %H:%M:%S')}
# Total proxies: {len(proxies)}
# Format: ip:port

"""
            for proxy in proxies[:50]:  # Limit to first 50
                content += f"{proxy}\n"

            content += f"""
# Note: These are free proxies and may be unstable
# Test before using with "Test Proxies" button
# For better reliability, consider paid proxy services
"""

            with open('example_proxies.txt', 'w', encoding='utf-8') as f:
                f.write(content)

            self.logger.info("Updated example_proxies.txt with fresh proxies")
            return True
        except Exception as e:
            self.logger.error(f"Error updating example_proxies.txt: {str(e)}")
            return False
