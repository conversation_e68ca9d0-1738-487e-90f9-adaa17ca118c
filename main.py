#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto Registration Tool with Proxy Support
Main GUI Application
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import json
import os
from datetime import datetime
import logging

from modules.proxy_manager import ProxyManager
from modules.registration_bot import RegistrationBot
from modules.data_generator import DataGenerator
from modules.proxy_fetcher import ProxyFetcher

class AutoRegistrationGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Auto Registration Tool v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Initialize components
        self.proxy_manager = ProxyManager()
        self.data_generator = DataGenerator()
        self.proxy_fetcher = ProxyFetcher()
        self.registration_bot = None

        # Threading
        self.is_running = False
        self.worker_thread = None
        self.message_queue = queue.Queue()

        # Setup logging
        self.setup_logging()

        # Create GUI
        self.create_widgets()

        # Start message processing
        self.process_queue()

    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('registration.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def create_widgets(self):
        """Create and arrange GUI widgets"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Configuration Tab
        self.create_config_tab(notebook)

        # Proxy Tab
        self.create_proxy_tab(notebook)

        # Control Tab
        self.create_control_tab(notebook)

        # Log Tab
        self.create_log_tab(notebook)

    def create_config_tab(self, notebook):
        """Create configuration tab"""
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="Configuration")

        # Target URL
        ttk.Label(config_frame, text="Target URL:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.url_entry = ttk.Entry(config_frame, width=60)
        self.url_entry.insert(0, "https://www.13win16.com/?id=*********")
        self.url_entry.grid(row=0, column=1, columnspan=2, padx=5, pady=5)

        # Registration settings
        settings_frame = ttk.LabelFrame(config_frame, text="Registration Settings")
        settings_frame.grid(row=1, column=0, columnspan=3, sticky="ew", padx=5, pady=10)

        ttk.Label(settings_frame, text="Number of accounts:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.account_count = tk.IntVar(value=10)
        ttk.Spinbox(settings_frame, from_=1, to=1000, textvariable=self.account_count, width=10).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(settings_frame, text="Delay between registrations (seconds):").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.delay_var = tk.IntVar(value=5)
        ttk.Spinbox(settings_frame, from_=1, to=60, textvariable=self.delay_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        # Browser settings
        browser_frame = ttk.LabelFrame(config_frame, text="Browser Settings")
        browser_frame.grid(row=2, column=0, columnspan=3, sticky="ew", padx=5, pady=10)

        self.headless_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(browser_frame, text="Headless mode", variable=self.headless_var).grid(row=0, column=0, sticky="w", padx=5, pady=5)

        self.use_proxy_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(browser_frame, text="Use proxy", variable=self.use_proxy_var).grid(row=0, column=1, sticky="w", padx=5, pady=5)

    def create_proxy_tab(self, notebook):
        """Create proxy management tab"""
        proxy_frame = ttk.Frame(notebook)
        notebook.add(proxy_frame, text="Proxy Management")

        # Proxy list
        ttk.Label(proxy_frame, text="Proxy List:").pack(anchor="w", padx=5, pady=5)

        proxy_list_frame = ttk.Frame(proxy_frame)
        proxy_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.proxy_text = scrolledtext.ScrolledText(proxy_list_frame, height=15)
        self.proxy_text.pack(fill=tk.BOTH, expand=True)

        # Proxy buttons
        proxy_btn_frame = ttk.Frame(proxy_frame)
        proxy_btn_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(proxy_btn_frame, text="Load from File", command=self.load_proxy_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(proxy_btn_frame, text="Test Proxies", command=self.test_proxies).pack(side=tk.LEFT, padx=5)
        ttk.Button(proxy_btn_frame, text="Auto Update VN", command=self.auto_update_vietnam_proxies).pack(side=tk.LEFT, padx=5)
        ttk.Button(proxy_btn_frame, text="Clear", command=lambda: self.proxy_text.delete(1.0, tk.END)).pack(side=tk.LEFT, padx=5)

        # Proxy status
        self.proxy_status_label = ttk.Label(proxy_frame, text="Proxy Status: Not loaded")
        self.proxy_status_label.pack(anchor="w", padx=5, pady=5)

    def create_control_tab(self, notebook):
        """Create control tab"""
        control_frame = ttk.Frame(notebook)
        notebook.add(control_frame, text="Control")

        # Progress
        ttk.Label(control_frame, text="Progress:").pack(anchor="w", padx=5, pady=5)
        self.progress_var = tk.StringVar(value="Ready")
        ttk.Label(control_frame, textvariable=self.progress_var).pack(anchor="w", padx=5)

        self.progress_bar = ttk.Progressbar(control_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # Statistics
        stats_frame = ttk.LabelFrame(control_frame, text="Statistics")
        stats_frame.pack(fill=tk.X, padx=5, pady=10)

        self.stats_text = tk.Text(stats_frame, height=8, state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Control buttons
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=10)

        self.start_btn = ttk.Button(btn_frame, text="Start Registration", command=self.start_registration)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(btn_frame, text="Stop", command=self.stop_registration, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(btn_frame, text="Save Results", command=self.save_results).pack(side=tk.LEFT, padx=5)

    def create_log_tab(self, notebook):
        """Create log tab"""
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="Logs")

        self.log_text = scrolledtext.ScrolledText(log_frame)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Log controls
        log_btn_frame = ttk.Frame(log_frame)
        log_btn_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(log_btn_frame, text="Clear Logs", command=lambda: self.log_text.delete(1.0, tk.END)).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_btn_frame, text="Save Logs", command=self.save_logs).pack(side=tk.LEFT, padx=5)

    def load_proxy_file(self):
        """Load proxy list from file"""
        file_path = filedialog.askopenfilename(
            title="Select Proxy File",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    proxies = f.read()
                self.proxy_text.delete(1.0, tk.END)
                self.proxy_text.insert(1.0, proxies)
                self.log_message(f"Loaded proxy file: {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load proxy file: {str(e)}")

    def test_proxies(self):
        """Test proxy connectivity"""
        proxy_list = self.proxy_text.get(1.0, tk.END).strip().split('\n')
        proxy_list = [p.strip() for p in proxy_list if p.strip()]

        if not proxy_list:
            messagebox.showwarning("Warning", "No proxies to test")
            return

        self.log_message("Testing proxies...")
        threading.Thread(target=self._test_proxies_worker, args=(proxy_list,), daemon=True).start()

    def _test_proxies_worker(self, proxy_list):
        """Worker thread for testing proxies"""
        working_proxies = self.proxy_manager.test_proxies(proxy_list)
        self.message_queue.put(('proxy_test_complete', len(working_proxies), len(proxy_list)))

    def auto_update_vietnam_proxies(self):
        """Auto update Vietnam proxies"""
        self.log_message("Starting auto update of Vietnam proxies...")
        threading.Thread(target=self._auto_update_worker, daemon=True).start()

    def _auto_update_worker(self):
        """Worker thread for auto updating proxies"""
        try:
            # Fetch fresh proxies
            self.message_queue.put(('proxy_update_status', 'Fetching fresh Vietnam proxies...'))
            proxies = self.proxy_fetcher.fetch_all_vietnam_proxies()

            if not proxies:
                self.message_queue.put(('proxy_update_error', 'No proxies found'))
                return

            self.message_queue.put(('proxy_update_status', f'Found {len(proxies)} proxies, testing...'))

            # Test proxies
            working_proxies = self.proxy_manager.test_proxies(proxies[:50], max_workers=20, timeout=5)

            if working_proxies:
                # Update proxy text area
                proxy_text = '\n'.join(working_proxies)
                self.message_queue.put(('proxy_update_success', proxy_text, len(working_proxies), len(proxies)))

                # Save to file
                self.proxy_fetcher.update_example_proxies(working_proxies)
            else:
                self.message_queue.put(('proxy_update_error', 'No working proxies found'))

        except Exception as e:
            self.message_queue.put(('proxy_update_error', str(e)))

    def start_registration(self):
        """Start the registration process"""
        if self.is_running:
            return

        # Validate inputs
        url = self.url_entry.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter target URL")
            return

        proxy_list = []
        if self.use_proxy_var.get():
            proxy_text = self.proxy_text.get(1.0, tk.END).strip()
            if proxy_text:
                proxy_list = [p.strip() for p in proxy_text.split('\n') if p.strip()]

            if not proxy_list:
                messagebox.showerror("Error", "Please load proxy list or disable proxy usage")
                return

        # Update UI
        self.is_running = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.progress_bar['maximum'] = self.account_count.get()
        self.progress_bar['value'] = 0

        # Start worker thread
        self.worker_thread = threading.Thread(
            target=self._registration_worker,
            args=(url, proxy_list),
            daemon=True
        )
        self.worker_thread.start()

    def _registration_worker(self, url, proxy_list):
        """Worker thread for registration process"""
        try:
            self.registration_bot = RegistrationBot(
                url=url,
                proxy_list=proxy_list,
                headless=self.headless_var.get(),
                delay=self.delay_var.get(),
                data_generator=self.data_generator
            )

            total_accounts = self.account_count.get()

            for i in range(total_accounts):
                if not self.is_running:
                    break

                try:
                    result = self.registration_bot.register_account()
                    self.message_queue.put(('registration_result', i + 1, result))
                except Exception as e:
                    self.message_queue.put(('registration_error', i + 1, str(e)))

        except Exception as e:
            self.message_queue.put(('worker_error', str(e)))
        finally:
            self.message_queue.put(('worker_complete',))

    def stop_registration(self):
        """Stop the registration process"""
        self.is_running = False
        if self.registration_bot:
            self.registration_bot.stop()

        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.log_message("Registration stopped by user")

    def process_queue(self):
        """Process messages from worker threads"""
        try:
            while True:
                message = self.message_queue.get_nowait()
                self.handle_message(message)
        except queue.Empty:
            pass

        # Schedule next check
        self.root.after(100, self.process_queue)

    def handle_message(self, message):
        """Handle messages from worker threads"""
        msg_type = message[0]

        if msg_type == 'proxy_test_complete':
            working, total = message[1], message[2]
            self.proxy_status_label.config(text=f"Proxy Status: {working}/{total} working")
            self.log_message(f"Proxy test complete: {working}/{total} working")

        elif msg_type == 'registration_result':
            count, result = message[1], message[2]
            self.progress_bar['value'] = count
            self.progress_var.set(f"Completed: {count}/{self.account_count.get()}")
            self.log_message(f"Account {count}: {result['status']}")
            self.update_stats(result)

        elif msg_type == 'registration_error':
            count, error = message[1], message[2]
            self.progress_bar['value'] = count
            self.log_message(f"Account {count}: ERROR - {error}")

        elif msg_type == 'worker_error':
            error = message[1]
            self.log_message(f"Worker error: {error}")
            self.stop_registration()

        elif msg_type == 'worker_complete':
            self.log_message("Registration process completed")
            self.stop_registration()

        elif msg_type == 'proxy_update_status':
            status = message[1]
            self.proxy_status_label.config(text=f"Proxy Status: {status}")
            self.log_message(status)

        elif msg_type == 'proxy_update_success':
            proxy_text, working_count, total_count = message[1], message[2], message[3]
            self.proxy_text.delete(1.0, tk.END)
            self.proxy_text.insert(1.0, proxy_text)
            self.proxy_status_label.config(text=f"Proxy Status: {working_count}/{total_count} working (auto-updated)")
            self.log_message(f"Auto-update completed: {working_count}/{total_count} working proxies")

        elif msg_type == 'proxy_update_error':
            error = message[1]
            self.proxy_status_label.config(text=f"Proxy Status: Update failed - {error}")
            self.log_message(f"Proxy update error: {error}")

    def update_stats(self, result):
        """Update statistics display"""
        # This will be implemented to show success/failure stats
        pass

    def log_message(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        self.logger.info(message)

    def save_results(self):
        """Save registration results"""
        # Implementation for saving results
        pass

    def save_logs(self):
        """Save logs to file"""
        file_path = filedialog.asksaveasfilename(
            title="Save Logs",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("Success", "Logs saved successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save logs: {str(e)}")

def main():
    root = tk.Tk()
    app = AutoRegistrationGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
