{"default_settings": {"target_url": "https://www.13win16.com/?id=*********", "account_count": 10, "delay_seconds": 5, "headless_mode": true, "use_proxy": true, "timeout_seconds": 30}, "browser_settings": {"window_size": "1920,1080", "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/121.0", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"]}, "field_mappings": {"username": ["username", "user", "login", "account", "ta<PERSON><PERSON><PERSON>"], "email": ["email", "mail", "e-mail", "email_address"], "password": ["password", "pass", "pwd", "<PERSON><PERSON><PERSON>"], "confirm_password": ["confirm_password", "password_confirm", "confirm_pass", "repassword", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "full_name": ["fullname", "full_name", "name", "realname", "hoten"], "first_name": ["firstname", "first_name", "fname", "ten"], "last_name": ["lastname", "last_name", "lname", "ho"], "phone": ["phone", "mobile", "tel", "telephone", "sodienthoai"], "birth_date": ["birthday", "birth_date", "dob", "birthdate", "ng<PERSON><PERSON><PERSON>"]}, "selectors": {"registration_forms": ["//form[contains(@class, 'register')]", "//form[contains(@class, 'signup')]", "//form[contains(@id, 'register')]", "//form[contains(@id, 'signup')]", "//div[contains(@class, 'register')]//form"], "registration_buttons": ["//a[contains(text(), '<PERSON><PERSON><PERSON> ký')]", "//a[contains(text(), 'Register')]", "//button[contains(text(), '<PERSON><PERSON>ng ký')]", "//button[contains(text(), 'Register')]"], "submit_buttons": ["//button[@type='submit']", "//input[@type='submit']", "//button[contains(text(), '<PERSON><PERSON>ng ký')]", "//button[contains(text(), 'Register')]", "//button[contains(text(), 'Submit')]"], "success_indicators": ["//*[contains(text(), 'thành công')]", "//*[contains(text(), 'success')]", "//*[contains(text(), 'welcome')]", "//*[contains(text(), 'chào mừng')]"], "error_indicators": ["//*[contains(text(), 'lỗi')]", "//*[contains(text(), 'error')]", "//*[contains(text(), 'failed')]", "//*[contains(text(), 'thất bại')]"]}}