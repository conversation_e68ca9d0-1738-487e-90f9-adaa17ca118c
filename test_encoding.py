#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test encoding compatibility
"""

def test_encoding():
    """Test if encoding issues are fixed"""
    print("Testing encoding compatibility...")
    
    # Test ASCII symbols
    symbols = {
        'OK': '[OK]',
        'ERROR': '[ERROR]',
        'FAIL': '[FAIL]',
        'WARNING': '[WARNING]',
        'SUCCESS': '[SUCCESS]'
    }
    
    for name, symbol in symbols.items():
        print(f"{name}: {symbol}")
    
    print("\n[OK] Encoding test completed successfully!")

if __name__ == "__main__":
    test_encoding()
