# Auto Registration Tool

Tool tự động đăng ký tài khoản với hỗ trợ proxy và giao diện GUI.

## Tính năng

- [OK] Giao diện GUI thân thiện với người dùng
- [OK] Hỗ trợ proxy (HTTP/HTTPS với hoặc không có authentication)
- [OK] Tạo dữ liệu người dùng ngẫu nhiên (tên, email, số điện thoại Việt Nam)
- [OK] Kiểm tra proxy tự động
- [OK] Đăng ký đa luồng với delay có thể tùy chỉnh
- [OK] Logging chi tiết và báo cáo kết quả
- [OK] Chế độ headless hoặc có giao diện browser
- [OK] Tự động phát hiện và điền form đăng ký
- [OK] Xử lý captcha cơ bản
- [OK] Thống kê thành công/thất bại

## <PERSON><PERSON><PERSON> c<PERSON>u hệ thống

- Python 3.7+
- Chrome Browser
- Windows/Linux/MacOS

## Cài đặt

1. Clone hoặc tải về project:
```bash
git clone <repository-url>
cd auto-registration-tool
```

2. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

3. Chạy ứng dụng:
```bash
python main.py
```

## Hướng dẫn sử dụng

### 1. Cấu hình cơ bản

- **Target URL**: Nhập URL trang web cần đăng ký
- **Number of accounts**: Số lượng tài khoản cần tạo
- **Delay**: Thời gian chờ giữa các lần đăng ký (giây)
- **Headless mode**: Chạy browser ẩn (nhanh hơn)
- **Use proxy**: Sử dụng proxy hay không

### 2. Quản lý Proxy

#### Định dạng proxy hỗ trợ:
```
# Proxy không authentication
ip:port
123.456.789.0:8080

# Proxy có authentication
username:password@ip:port
user123:pass456@123.456.789.0:8080
```

#### Cách thêm proxy:
1. Vào tab "Proxy Management"
2. Dán danh sách proxy vào ô text (mỗi proxy một dòng)
3. Hoặc click "Load from File" để tải từ file .txt
4. Click "Test Proxies" để kiểm tra proxy hoạt động

### 3. Chạy đăng ký

1. Cấu hình đầy đủ ở tab "Configuration"
2. Thêm proxy ở tab "Proxy Management" (nếu sử dụng)
3. Vào tab "Control" và click "Start Registration"
4. Theo dõi tiến trình và kết quả
5. Click "Stop" để dừng bất cứ lúc nào

### 4. Xem kết quả

- Tab "Control": Thống kê tổng quan và tiến trình
- Tab "Logs": Log chi tiết của quá trình đăng ký
- Click "Save Results" để lưu kết quả
- Click "Save Logs" để lưu log

## Cấu trúc Project

```
auto-registration-tool/
├── main.py                 # File chính chạy GUI
├── requirements.txt        # Dependencies
├── README.md              # Hướng dẫn
├── modules/
│   ├── __init__.py
│   ├── proxy_manager.py   # Quản lý proxy
│   ├── data_generator.py  # Tạo dữ liệu ngẫu nhiên
│   └── registration_bot.py # Bot đăng ký chính
└── logs/
    └── registration.log   # File log
```

## Tùy chỉnh cho trang web cụ thể

Tool được thiết kế để tự động phát hiện form đăng ký, nhưng có thể cần tùy chỉnh cho một số trang web:

1. Mở file `modules/registration_bot.py`
2. Chỉnh sửa các selector trong:
   - `find_and_fill_registration_form()`: Tìm form đăng ký
   - `fill_registration_fields()`: Mapping tên field
   - `submit_registration_form()`: Tìm nút submit
   - `check_registration_result()`: Kiểm tra kết quả

## Lưu ý quan trọng

[WARNING] **Sử dụng có trách nhiệm**:
- Chỉ sử dụng cho mục đích hợp pháp
- Tuân thủ Terms of Service của trang web
- Không spam hoặc tạo tài khoản ảo
- Sử dụng delay hợp lý để không gây quá tải server

[WARNING] **Bảo mật**:
- Không chia sẻ proxy có authentication
- Kiểm tra proxy từ nguồn tin cậy
- Không lưu mật khẩu trong code

## Xử lý lỗi thường gặp

### 1. ChromeDriver không tìm thấy
```bash
# Cài đặt lại webdriver-manager
pip install --upgrade webdriver-manager
```

### 2. Proxy không hoạt động
- Kiểm tra định dạng proxy
- Test proxy trước khi sử dụng
- Thử proxy khác

### 3. Form không được điền
- Kiểm tra selector trong code
- Chạy ở chế độ không headless để debug
- Xem screenshot được tạo

### 4. Captcha
- Tool chỉ phát hiện captcha, không giải được
- Cần can thiệp thủ công hoặc sử dụng service giải captcha

## Liên hệ hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra log file
2. Chạy ở chế độ debug (không headless)
3. Tạo issue với thông tin chi tiết

## License

MIT License - Sử dụng tự do cho mục đích cá nhân và thương mại.
