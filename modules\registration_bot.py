#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Registration Bot Module
Handles automated registration using Selenium
"""

import time
import random
import logging
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

class RegistrationBot:
    def __init__(self, url, proxy_list=None, headless=True, delay=5, data_generator=None):
        self.url = url
        self.proxy_list = proxy_list or []
        self.headless = headless
        self.delay = delay
        self.data_generator = data_generator
        self.logger = logging.getLogger(__name__)
        self.driver = None
        self.is_running = True
        self.results = []

        # Statistics
        self.stats = {
            'total_attempts': 0,
            'successful': 0,
            'failed': 0,
            'errors': []
        }

    def create_driver(self, proxy=None):
        """Create Chrome WebDriver with optional proxy"""
        try:
            chrome_options = Options()

            if self.headless:
                chrome_options.add_argument('--headless')

            # Basic Chrome options
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')
            chrome_options.add_argument('--window-size=1920,1080')

            # Random user agent
            if self.data_generator:
                user_agent = self.data_generator.generate_user_agent()
                chrome_options.add_argument(f'--user-agent={user_agent}')

            # Proxy configuration
            if proxy:
                from .proxy_manager import ProxyManager
                proxy_manager = ProxyManager()
                proxy_config = proxy_manager.format_proxy_for_selenium(proxy)

                if proxy_config:
                    if 'username' in proxy_config:
                        # Authenticated proxy
                        chrome_options.add_argument(f'--proxy-server=http://{proxy_config["ip"]}:{proxy_config["port"]}')
                        # Note: For authenticated proxies, you might need additional setup
                    else:
                        # Simple proxy
                        chrome_options.add_argument(f'--proxy-server=http://{proxy_config["ip"]}:{proxy_config["port"]}')

            # Create driver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(30)

            return driver

        except Exception as e:
            self.logger.error(f"Failed to create driver: {str(e)}")
            return None

    def wait_for_element(self, driver, by, value, timeout=10):
        """Wait for element to be present"""
        try:
            element = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            return None

    def wait_for_clickable(self, driver, by, value, timeout=10):
        """Wait for element to be clickable"""
        try:
            element = WebDriverWait(driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            return None

    def fill_form_field(self, driver, field_selector, value, by=By.NAME):
        """Fill a form field with value"""
        try:
            element = self.wait_for_element(driver, by, field_selector)
            if element:
                element.clear()
                time.sleep(0.5)
                element.send_keys(value)
                time.sleep(0.5)
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to fill field {field_selector}: {str(e)}")
            return False

    def solve_simple_captcha(self, driver):
        """Attempt to solve simple captcha"""
        try:
            # Look for common captcha patterns
            captcha_selectors = [
                "//img[contains(@src, 'captcha')]",
                "//img[contains(@alt, 'captcha')]",
                "//*[contains(@class, 'captcha')]",
                "//input[contains(@name, 'captcha')]"
            ]

            for selector in captcha_selectors:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    self.logger.warning("Captcha detected - manual intervention may be required")
                    return False

            return True
        except Exception as e:
            self.logger.error(f"Captcha solving error: {str(e)}")
            return False

    def register_account(self):
        """Register a single account"""
        if not self.is_running:
            return {'status': 'stopped', 'message': 'Registration stopped'}

        self.stats['total_attempts'] += 1
        proxy = None

        try:
            # Get proxy if available
            if self.proxy_list:
                proxy = random.choice(self.proxy_list)

            # Generate user data
            if not self.data_generator:
                raise Exception("Data generator not available")

            user_data = self.data_generator.generate_user_data()

            # Create driver
            self.driver = self.create_driver(proxy)
            if not self.driver:
                raise Exception("Failed to create WebDriver")

            # Navigate to registration page
            self.logger.info(f"Navigating to: {self.url}")
            self.driver.get(self.url)
            time.sleep(3)

            # Take screenshot for debugging
            try:
                self.driver.save_screenshot(f"screenshot_{int(time.time())}.png")
            except:
                pass

            # Look for registration form or button
            registration_result = self.find_and_fill_registration_form(user_data)

            if registration_result['success']:
                self.stats['successful'] += 1
                result = {
                    'status': 'success',
                    'message': 'Account registered successfully',
                    'data': user_data,
                    'proxy': proxy,
                    'timestamp': time.time()
                }
            else:
                self.stats['failed'] += 1
                result = {
                    'status': 'failed',
                    'message': registration_result['message'],
                    'data': user_data,
                    'proxy': proxy,
                    'timestamp': time.time()
                }

            self.results.append(result)
            return result

        except Exception as e:
            self.stats['failed'] += 1
            error_msg = str(e)
            self.stats['errors'].append(error_msg)

            self.logger.error(f"Registration failed: {error_msg}")

            return {
                'status': 'error',
                'message': error_msg,
                'proxy': proxy,
                'timestamp': time.time()
            }

        finally:
            # Clean up
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None

            # Delay between registrations
            if self.is_running:
                time.sleep(self.delay)

    def stop(self):
        """Stop the registration process"""
        self.is_running = False
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass

    def get_stats(self):
        """Get registration statistics"""
        return self.stats.copy()

    def get_results(self):
        """Get all registration results"""
        return self.results.copy()

    def find_and_fill_registration_form(self, user_data):
        """Find and fill registration form"""
        try:
            # Common registration form selectors
            form_selectors = [
                "//form[contains(@class, 'register')]",
                "//form[contains(@class, 'signup')]",
                "//form[contains(@id, 'register')]",
                "//form[contains(@id, 'signup')]",
                "//div[contains(@class, 'register')]//form",
                "//div[contains(@class, 'signup')]//form"
            ]

            form_element = None
            for selector in form_selectors:
                elements = self.driver.find_elements(By.XPATH, selector)
                if elements:
                    form_element = elements[0]
                    break

            if not form_element:
                # Look for registration button first
                reg_button_selectors = [
                    "//a[contains(text(), 'Đăng ký')]",
                    "//a[contains(text(), 'Register')]",
                    "//button[contains(text(), 'Đăng ký')]",
                    "//button[contains(text(), 'Register')]",
                    "//*[contains(@class, 'register')]",
                    "//*[contains(@class, 'signup')]"
                ]

                for selector in reg_button_selectors:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        elements[0].click()
                        time.sleep(3)
                        break

                # Try to find form again
                for selector in form_selectors:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        form_element = elements[0]
                        break

            if not form_element:
                return {'success': False, 'message': 'Registration form not found'}

            # Fill form fields
            success = self.fill_registration_fields(user_data)

            if success:
                # Submit form
                return self.submit_registration_form()
            else:
                return {'success': False, 'message': 'Failed to fill form fields'}

        except Exception as e:
            return {'success': False, 'message': f'Form processing error: {str(e)}'}

    def fill_registration_fields(self, user_data):
        """Fill registration form fields"""
        try:
            # Common field mappings
            field_mappings = {
                'username': ['username', 'user', 'login', 'account'],
                'email': ['email', 'mail', 'e-mail'],
                'password': ['password', 'pass', 'pwd'],
                'confirm_password': ['confirm_password', 'password_confirm', 'confirm_pass', 'repassword'],
                'full_name': ['fullname', 'full_name', 'name', 'realname'],
                'first_name': ['firstname', 'first_name', 'fname'],
                'last_name': ['lastname', 'last_name', 'lname'],
                'phone': ['phone', 'mobile', 'tel', 'telephone'],
                'birth_date': ['birthday', 'birth_date', 'dob', 'birthdate']
            }

            filled_fields = 0

            for data_key, field_names in field_mappings.items():
                if data_key in user_data:
                    value = user_data[data_key]

                    # Convert date to string if needed
                    if data_key == 'birth_date':
                        value = value.strftime('%d/%m/%Y')

                    # Try different field selectors
                    field_found = False
                    for field_name in field_names:
                        selectors = [
                            f"//input[@name='{field_name}']",
                            f"//input[@id='{field_name}']",
                            f"//input[contains(@name, '{field_name}')]",
                            f"//input[contains(@id, '{field_name}')]",
                            f"//input[contains(@placeholder, '{field_name}')]"
                        ]

                        for selector in selectors:
                            elements = self.driver.find_elements(By.XPATH, selector)
                            if elements:
                                try:
                                    element = elements[0]
                                    element.clear()
                                    element.send_keys(str(value))
                                    filled_fields += 1
                                    field_found = True
                                    self.logger.info(f"Filled {field_name}: {value}")
                                    time.sleep(0.5)
                                    break
                                except Exception as e:
                                    self.logger.warning(f"Failed to fill {field_name}: {str(e)}")

                        if field_found:
                            break

            self.logger.info(f"Filled {filled_fields} form fields")
            return filled_fields > 0

        except Exception as e:
            self.logger.error(f"Error filling fields: {str(e)}")
            return False

    def submit_registration_form(self):
        """Submit the registration form"""
        try:
            # Check for captcha first
            if not self.solve_simple_captcha(self.driver):
                return {'success': False, 'message': 'Captcha verification failed'}

            # Look for submit button
            submit_selectors = [
                "//button[@type='submit']",
                "//input[@type='submit']",
                "//button[contains(text(), 'Đăng ký')]",
                "//button[contains(text(), 'Register')]",
                "//button[contains(text(), 'Submit')]",
                "//button[contains(@class, 'submit')]",
                "//input[contains(@value, 'Đăng ký')]",
                "//input[contains(@value, 'Register')]"
            ]

            submit_button = None
            for selector in submit_selectors:
                elements = self.driver.find_elements(By.XPATH, selector)
                if elements:
                    submit_button = elements[0]
                    break

            if not submit_button:
                return {'success': False, 'message': 'Submit button not found'}

            # Click submit button
            submit_button.click()
            time.sleep(5)

            # Check for success/error messages
            return self.check_registration_result()

        except Exception as e:
            return {'success': False, 'message': f'Submit error: {str(e)}'}

    def check_registration_result(self):
        """Check if registration was successful"""
        try:
            # Success indicators
            success_selectors = [
                "//*[contains(text(), 'thành công')]",
                "//*[contains(text(), 'success')]",
                "//*[contains(text(), 'welcome')]",
                "//*[contains(text(), 'chào mừng')]",
                "//*[contains(@class, 'success')]",
                "//*[contains(@class, 'welcome')]"
            ]

            # Error indicators
            error_selectors = [
                "//*[contains(text(), 'lỗi')]",
                "//*[contains(text(), 'error')]",
                "//*[contains(text(), 'failed')]",
                "//*[contains(text(), 'thất bại')]",
                "//*[contains(@class, 'error')]",
                "//*[contains(@class, 'alert')]"
            ]

            # Check for success
            for selector in success_selectors:
                elements = self.driver.find_elements(By.XPATH, selector)
                if elements:
                    return {'success': True, 'message': 'Registration successful'}

            # Check for errors
            for selector in error_selectors:
                elements = self.driver.find_elements(By.XPATH, selector)
                if elements:
                    error_text = elements[0].text
                    return {'success': False, 'message': f'Registration failed: {error_text}'}

            # Check URL change (might indicate success)
            current_url = self.driver.current_url
            if current_url != self.url and 'login' not in current_url.lower():
                return {'success': True, 'message': 'Registration completed (URL changed)'}

            # Default to uncertain
            return {'success': False, 'message': 'Registration result unclear'}

        except Exception as e:
            return {'success': False, 'message': f'Result check error: {str(e)}'}
