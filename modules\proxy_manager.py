#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Proxy Manager Mo<PERSON>le
Handles proxy loading, testing, and rotation
"""

import requests
import random
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

class ProxyManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.working_proxies = []
        self.current_proxy_index = 0
        self.lock = threading.Lock()

    def parse_proxy(self, proxy_string):
        """Parse proxy string into components"""
        try:
            proxy_string = proxy_string.strip()

            # Handle different proxy formats
            if '@' in proxy_string:
                # Format: username:password@ip:port
                auth_part, address_part = proxy_string.split('@')
                username, password = auth_part.split(':')
                ip, port = address_part.split(':')

                return {
                    'ip': ip,
                    'port': int(port),
                    'username': username,
                    'password': password,
                    'auth': True
                }
            else:
                # Format: ip:port
                ip, port = proxy_string.split(':')
                return {
                    'ip': ip,
                    'port': int(port),
                    'auth': False
                }
        except Exception as e:
            self.logger.error(f"Failed to parse proxy: {proxy_string} - {str(e)}")
            return None

    def test_single_proxy(self, proxy_string, timeout=10):
        """Test a single proxy"""
        proxy_info = self.parse_proxy(proxy_string)
        if not proxy_info:
            return False, proxy_string, "Invalid format"

        try:
            # Prepare proxy configuration
            if proxy_info['auth']:
                proxy_url = f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['ip']}:{proxy_info['port']}"
            else:
                proxy_url = f"http://{proxy_info['ip']}:{proxy_info['port']}"

            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }

            # Test proxy with a simple request
            test_urls = [
                'http://httpbin.org/ip',
                'http://icanhazip.com',
                'http://ipinfo.io/ip'
            ]

            for test_url in test_urls:
                try:
                    response = requests.get(
                        test_url,
                        proxies=proxies,
                        timeout=timeout,
                        headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                    )

                    if response.status_code == 200:
                        return True, proxy_string, f"Working - IP: {response.text.strip()}"
                except:
                    continue

            return False, proxy_string, "Connection failed"

        except Exception as e:
            return False, proxy_string, f"Error: {str(e)}"

    def test_proxies(self, proxy_list, max_workers=50, timeout=10):
        """Test multiple proxies concurrently"""
        self.logger.info(f"Testing {len(proxy_list)} proxies...")
        working_proxies = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all proxy tests
            future_to_proxy = {
                executor.submit(self.test_single_proxy, proxy, timeout): proxy
                for proxy in proxy_list
            }

            # Collect results
            for future in as_completed(future_to_proxy):
                try:
                    is_working, proxy, message = future.result()
                    if is_working:
                        working_proxies.append(proxy)
                        self.logger.info(f"[OK] {proxy} - {message}")
                    else:
                        self.logger.warning(f"[FAIL] {proxy} - {message}")
                except Exception as e:
                    proxy = future_to_proxy[future]
                    self.logger.error(f"[ERROR] {proxy} - Exception: {str(e)}")

        self.working_proxies = working_proxies
        self.logger.info(f"Proxy test completed: {len(working_proxies)}/{len(proxy_list)} working")
        return working_proxies

    def get_next_proxy(self):
        """Get next proxy in rotation"""
        with self.lock:
            if not self.working_proxies:
                return None

            proxy = self.working_proxies[self.current_proxy_index]
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.working_proxies)
            return proxy

    def get_random_proxy(self):
        """Get random proxy from working list"""
        if not self.working_proxies:
            return None
        return random.choice(self.working_proxies)

    def format_proxy_for_selenium(self, proxy_string):
        """Format proxy for selenium webdriver"""
        proxy_info = self.parse_proxy(proxy_string)
        if not proxy_info:
            return None

        if proxy_info['auth']:
            return {
                'http': f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['ip']}:{proxy_info['port']}",
                'https': f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['ip']}:{proxy_info['port']}",
                'ip': proxy_info['ip'],
                'port': proxy_info['port'],
                'username': proxy_info['username'],
                'password': proxy_info['password']
            }
        else:
            return {
                'http': f"http://{proxy_info['ip']}:{proxy_info['port']}",
                'https': f"http://{proxy_info['ip']}:{proxy_info['port']}",
                'ip': proxy_info['ip'],
                'port': proxy_info['port']
            }

    def remove_proxy(self, proxy_string):
        """Remove a proxy from working list (if it fails)"""
        with self.lock:
            if proxy_string in self.working_proxies:
                self.working_proxies.remove(proxy_string)
                self.logger.warning(f"Removed failed proxy: {proxy_string}")

                # Adjust current index if needed
                if self.current_proxy_index >= len(self.working_proxies) and self.working_proxies:
                    self.current_proxy_index = 0

    def get_proxy_count(self):
        """Get number of working proxies"""
        return len(self.working_proxies)

    def load_proxies_from_file(self, file_path):
        """Load proxies from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                proxy_list = [line.strip() for line in f if line.strip()]

            self.logger.info(f"Loaded {len(proxy_list)} proxies from {file_path}")
            return proxy_list
        except Exception as e:
            self.logger.error(f"Failed to load proxies from {file_path}: {str(e)}")
            return []

    def save_working_proxies(self, file_path):
        """Save working proxies to file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for proxy in self.working_proxies:
                    f.write(f"{proxy}\n")

            self.logger.info(f"Saved {len(self.working_proxies)} working proxies to {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to save proxies to {file_path}: {str(e)}")
            return False
